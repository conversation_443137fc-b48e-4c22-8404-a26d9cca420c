import React, { useState, useEffect } from 'react';
import { Modal, Button, Steps, Typography, Space, Progress, Card, message } from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  BellOutlined,
  FileTextOutlined,
  TrophyOutlined,
  RightOutlined,
  LeftOutlined,
  CloseOutlined,
  PlayCircleOutlined,
  BookOutlined,
} from '@ant-design/icons';
import InteractiveGuide from './InteractiveGuide';
import FeatureIntroduction from './FeatureIntroduction';
import OperationTips, { commonTips, useOperationTips } from './OperationTips';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface GuideStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  target?: string;
  icon: React.ReactNode;
  action?: {
    text: string;
    onClick: () => void;
  };
}

interface UserGuideProps {
  visible: boolean;
  onClose: () => void;
  onComplete: () => void;
  onSkip: () => void;
  showFeatureIntro?: boolean;
  enableInteractiveGuide?: boolean;
  enableOperationTips?: boolean;
}

const UserGuide: React.FC<UserGuideProps> = ({
  visible,
  onClose,
  onComplete,
  onSkip,
  showFeatureIntro = false,
  enableInteractiveGuide = true,
  enableOperationTips = true,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [featureIntroVisible, setFeatureIntroVisible] = useState(false);
  const [interactiveGuideVisible, setInteractiveGuideVisible] = useState(false);

  // 操作提示管理
  const operationTips = useOperationTips(commonTips);

  // 交互式引导步骤
  const interactiveSteps = [
    {
      id: 'dashboard',
      title: '仪表板概览',
      content: '这里显示您的新闻摘要和重要统计信息',
      target: '[data-guide="dashboard"]',
      position: 'bottom' as const,
      highlight: true,
    },
    {
      id: 'sidebar',
      title: '导航菜单',
      content: '使用左侧菜单可以快速访问不同功能模块',
      target: '[data-guide="sidebar"]',
      position: 'right' as const,
      highlight: true,
    },
    {
      id: 'search',
      title: '全局搜索',
      content: '点击搜索图标或按 Ctrl+K 快速搜索新闻',
      target: '[data-guide="search"]',
      position: 'bottom' as const,
      highlight: true,
    },
    {
      id: 'notifications',
      title: '通知中心',
      content: '查看最新的推送通知和系统消息',
      target: '[data-guide="notifications"]',
      position: 'bottom' as const,
      highlight: true,
    },
    {
      id: 'profile',
      title: '个人中心',
      content: '管理您的个人信息和系统设置',
      target: '[data-guide="profile"]',
      position: 'bottom' as const,
      highlight: true,
    },
  ];

  const guideSteps: GuideStep[] = [
    {
      id: 'welcome',
      title: '欢迎使用财经新闻Bot',
      description: '让我们开始您的财经资讯之旅',
      icon: <TrophyOutlined />,
      content: (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎉</div>
          <Title level={3}>欢迎加入财经新闻Bot！</Title>
          <Paragraph>
            我们将通过5个简单步骤，帮助您快速上手，
            开始接收个性化的财经新闻推送。
          </Paragraph>
          <Card style={{ marginTop: '16px', backgroundColor: '#f6ffed' }}>
            <Space direction="vertical">
              <Text strong>您将学会：</Text>
              <Text>• 完善个人资料</Text>
              <Text>• 创建订阅配置</Text>
              <Text>• 设置推送偏好</Text>
              <Text>• 接收第一条新闻</Text>
            </Space>
          </Card>
        </div>
      ),
    },
    {
      id: 'profile',
      title: '完善个人资料',
      description: '设置您的基本信息，获得更好的体验',
      icon: <UserOutlined />,
      target: '#profile-section',
      content: (
        <div>
          <Title level={4}>完善您的个人资料</Title>
          <Paragraph>
            完善个人资料可以帮助我们为您提供更精准的新闻推荐。
          </Paragraph>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small">
              <Space>
                <UserOutlined style={{ color: '#1890ff' }} />
                <div>
                  <Text strong>上传头像</Text>
                  <br />
                  <Text type="secondary">让您的账户更个性化</Text>
                </div>
              </Space>
            </Card>
            <Card size="small">
              <Space>
                <SettingOutlined style={{ color: '#52c41a' }} />
                <div>
                  <Text strong>填写职业信息</Text>
                  <br />
                  <Text type="secondary">获得相关行业新闻推荐</Text>
                </div>
              </Space>
            </Card>
          </Space>
        </div>
      ),
      action: {
        text: '前往完善资料',
        onClick: () => {
          // 跳转到个人资料页面
          window.location.href = '/profile';
        },
      },
    },
    {
      id: 'subscription',
      title: '创建您的第一个订阅',
      description: '设置关注的财经主题和关键词',
      icon: <BellOutlined />,
      target: '#subscription-section',
      content: (
        <div>
          <Title level={4}>创建个性化订阅</Title>
          <Paragraph>
            通过设置订阅，您可以接收到最感兴趣的财经新闻。
          </Paragraph>
          <Steps direction="vertical" size="small" current={-1}>
            <Step title="选择主题" description="财经、科技、政策等" />
            <Step title="设置关键词" description="如：人工智能、新能源" />
            <Step title="配置推送渠道" description="邮箱、微信等" />
            <Step title="设置推送时间" description="选择接收新闻的时间" />
          </Steps>
        </div>
      ),
      action: {
        text: '创建订阅',
        onClick: () => {
          window.location.href = '/subscriptions/create';
        },
      },
    },
    {
      id: 'notification',
      title: '设置推送偏好',
      description: '选择您希望接收通知的方式和时间',
      icon: <SettingOutlined />,
      target: '#notification-section',
      content: (
        <div>
          <Title level={4}>配置通知设置</Title>
          <Paragraph>
            合理的通知设置可以确保您及时获取重要信息，
            同时避免过度打扰。
          </Paragraph>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small" style={{ backgroundColor: '#fff7e6' }}>
              <Text strong>推荐设置：</Text>
              <br />
              <Text>• 工作日 9:00 接收日报</Text>
              <Text>• 重要新闻实时推送</Text>
              <Text>• 晚上10点后免打扰</Text>
            </Card>
          </Space>
        </div>
      ),
      action: {
        text: '设置通知',
        onClick: () => {
          window.location.href = '/settings';
        },
      },
    },
    {
      id: 'complete',
      title: '开始使用',
      description: '一切就绪，开始您的财经资讯之旅',
      icon: <FileTextOutlined />,
      content: (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🚀</div>
          <Title level={3}>设置完成！</Title>
          <Paragraph>
            恭喜您完成了所有设置！现在您可以：
          </Paragraph>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card>
              <Space>
                <FileTextOutlined style={{ color: '#1890ff' }} />
                <Text>浏览最新财经新闻</Text>
              </Space>
            </Card>
            <Card>
              <Space>
                <BellOutlined style={{ color: '#52c41a' }} />
                <Text>接收个性化推送</Text>
              </Space>
            </Card>
            <Card>
              <Space>
                <TrophyOutlined style={{ color: '#faad14' }} />
                <Text>管理您的订阅</Text>
              </Space>
            </Card>
          </Space>
          <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px' }}>
            <Text type="success" strong>
              🎁 新用户奖励：免费使用高级功能30天！
            </Text>
          </div>
        </div>
      ),
      action: {
        text: '开始使用',
        onClick: () => {
          onComplete();
        },
      },
    },
  ];

  const handleNext = () => {
    if (currentStep < guideSteps.length - 1) {
      setCompletedSteps(prev => new Set(Array.from(prev).concat(currentStep)));
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleStepClick = (step: number) => {
    setCurrentStep(step);
  };

  const handleSkip = () => {
    Modal.confirm({
      title: '确认跳过引导？',
      content: '跳过引导后，您可以在帮助中心重新查看使用说明。',
      onOk: () => {
        onSkip();
      },
    });
  };

  const handleShowFeatureIntro = () => {
    setFeatureIntroVisible(true);
  };

  const handleStartInteractiveGuide = () => {
    onClose(); // 关闭当前引导
    setInteractiveGuideVisible(true);
  };

  const handleCompleteInteractiveGuide = () => {
    setInteractiveGuideVisible(false);
    message.success('引导完成！您已经掌握了基本操作');

    // 启用操作提示
    if (enableOperationTips) {
      operationTips.enableTips();
    }

    onComplete();
  };

  const handleCompleteGuide = () => {
    if (enableInteractiveGuide) {
      handleStartInteractiveGuide();
    } else {
      onComplete();
    }
  };

  // 初始化时显示功能介绍
  useEffect(() => {
    if (visible && showFeatureIntro) {
      setTimeout(() => {
        setFeatureIntroVisible(true);
      }, 500);
    }
  }, [visible, showFeatureIntro]);

  const progress = ((currentStep + 1) / guideSteps.length) * 100;
  const currentGuideStep = guideSteps[currentStep];

  return (
    <>
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <span>新手引导</span>
            <Text type="secondary">({currentStep + 1}/{guideSteps.length})</Text>
          </Space>
          <Button type="text" icon={<CloseOutlined />} onClick={handleSkip} />
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
      closable={false}
    >
      <div style={{ marginBottom: '24px' }}>
        <Progress percent={progress} showInfo={false} strokeColor="#1890ff" />
      </div>

      {/* 步骤指示器 */}
      <Steps
        current={currentStep}
        size="small"
        style={{ marginBottom: '24px' }}
        onChange={handleStepClick}
      >
        {guideSteps.map((step, index) => (
          <Step
            key={step.id}
            title={step.title}
            icon={step.icon}
            status={
              completedSteps.has(index)
                ? 'finish'
                : index === currentStep
                ? 'process'
                : 'wait'
            }
          />
        ))}
      </Steps>

      {/* 当前步骤内容 */}
      <div style={{ minHeight: '300px', marginBottom: '24px' }}>
        {currentGuideStep.content}
      </div>

      {/* 操作按钮 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button onClick={handleSkip} type="text">
          跳过引导
        </Button>
        
        <Space>
          {currentStep > 0 && (
            <Button icon={<LeftOutlined />} onClick={handlePrev}>
              上一步
            </Button>
          )}
          
          {currentStep < guideSteps.length - 1 ? (
            <Button type="primary" onClick={handleNext}>
              下一步
              <RightOutlined />
            </Button>
          ) : (
            <Space>
              <Button onClick={handleShowFeatureIntro} icon={<BookOutlined />}>
                功能介绍
              </Button>
              <Button type="primary" onClick={currentGuideStep.action?.onClick || handleCompleteGuide}>
                {enableInteractiveGuide ? '开始交互引导' : (currentGuideStep.action?.text || '完成')}
                {enableInteractiveGuide && <PlayCircleOutlined />}
              </Button>
            </Space>
          )}
        </Space>
      </div>
    </Modal>

    {/* 功能介绍弹窗 */}
    <FeatureIntroduction
      visible={featureIntroVisible}
      onClose={() => setFeatureIntroVisible(false)}
      onStartTour={handleStartInteractiveGuide}
      showNewFeatures={true}
    />

    {/* 交互式引导 */}
    <InteractiveGuide
      steps={interactiveSteps}
      visible={interactiveGuideVisible}
      onComplete={handleCompleteInteractiveGuide}
      onSkip={() => {
        setInteractiveGuideVisible(false);
        onComplete();
      }}
    />

    {/* 操作提示 */}
    {enableOperationTips && (
      <OperationTips
        tips={operationTips.tips}
        enabled={operationTips.enabled}
        onTipDismiss={operationTips.removeTip}
        onAllTipsDismiss={operationTips.disableTips}
      />
    )}
  </>
  );
};

export default UserGuide;
