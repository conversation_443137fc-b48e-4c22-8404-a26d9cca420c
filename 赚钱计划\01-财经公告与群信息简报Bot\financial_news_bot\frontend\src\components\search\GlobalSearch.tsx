import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Input,
  List,
  Typography,
  Space,
  Tag,
  Empty,
  Spin,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  HistoryOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'news' | 'subscription' | 'user' | 'setting' | 'page';
  url: string;
  icon: React.ReactNode;
  tags?: string[];
  timestamp?: string;
}

interface GlobalSearchProps {
  visible: boolean;
  onClose: () => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ visible, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // 真实搜索结果状态（移除所有模拟数据）

  // 获取最近搜索记录
  useEffect(() => {
    const recent = localStorage.getItem('recent_searches');
    if (recent) {
      setRecentSearches(JSON.parse(recent));
    }
  }, []);

  // 保存搜索记录
  const saveSearchQuery = (query: string) => {
    if (!query.trim()) return;
    
    const updated = [query, ...recentSearches.filter(item => item !== query)].slice(0, 10);
    setRecentSearches(updated);
    localStorage.setItem('recent_searches', JSON.stringify(updated));
  };

  // 执行真实搜索
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);

    try {
      // 真实API调用
      const response = await fetch(`/api/v1/search?q=${encodeURIComponent(query)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
        setSelectedIndex(0);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      // 搜索失败错误日志 - 用于生产环境错误监控
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索输入
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    performSearch(value);
  };

  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!searchResults.length) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : searchResults.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (searchResults[selectedIndex]) {
          handleResultClick(searchResults[selectedIndex]);
        }
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  // 处理结果点击
  const handleResultClick = (result: SearchResult) => {
    saveSearchQuery(searchQuery);
    navigate(result.url);
    onClose();
  };

  // 处理最近搜索点击
  const handleRecentSearchClick = (query: string) => {
    setSearchQuery(query);
    performSearch(query);
  };

  // 清除最近搜索
  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recent_searches');
  };

  // 获取类型图标和颜色
  const getTypeConfig = (type: string) => {
    const configs = {
      news: { color: '#1890ff', text: '新闻' },
      subscription: { color: '#52c41a', text: '订阅' },
      user: { color: '#722ed1', text: '用户' },
      setting: { color: '#faad14', text: '设置' },
      page: { color: '#13c2c2', text: '页面' },
    };
    return configs[type as keyof typeof configs] || configs.page;
  };

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSearchQuery('');
      setSearchResults([]);
      setSelectedIndex(0);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [visible]);

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
      closable={false}
      styles={{
        body: { padding: 0 },
      }}
    >
      <div style={{ padding: '16px 16px 0 16px' }}>
        <Input
          ref={inputRef}
          size="large"
          placeholder="搜索新闻、订阅、设置..."
          prefix={<SearchOutlined />}
          value={searchQuery}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          style={{ marginBottom: '16px' }}
        />
      </div>

      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" />
          </div>
        ) : searchQuery ? (
          searchResults.length > 0 ? (
            <List
              dataSource={searchResults}
              renderItem={(item, index) => (
                <List.Item
                  style={{
                    padding: '12px 16px',
                    cursor: 'pointer',
                    backgroundColor: index === selectedIndex ? '#f5f5f5' : 'transparent',
                  }}
                  onClick={() => handleResultClick(item)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{ 
                        width: 32, 
                        height: 32, 
                        borderRadius: '50%', 
                        backgroundColor: getTypeConfig(item.type).color + '20',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: getTypeConfig(item.type).color,
                      }}>
                        {item.icon}
                      </div>
                    }
                    title={
                      <Space>
                        <Text strong>{item.title}</Text>
                        <Tag color={getTypeConfig(item.type).color}>
                          {getTypeConfig(item.type).text}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">{item.description}</Text>
                        {item.tags && (
                          <div style={{ marginTop: '4px' }}>
                            {item.tags.map(tag => (
                              <Tag key={tag}>{tag}</Tag>
                            ))}
                          </div>
                        )}
                        {item.timestamp && (
                          <div style={{ marginTop: '4px' }}>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              <ClockCircleOutlined /> {item.timestamp}
                            </Text>
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty
              description="未找到相关结果"
              style={{ padding: '40px' }}
            />
          )
        ) : (
          <div style={{ padding: '16px' }}>
            {recentSearches.length > 0 ? (
              <>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  marginBottom: '12px' 
                }}>
                  <Text strong>
                    <HistoryOutlined /> 最近搜索
                  </Text>
                  <Text 
                    type="secondary" 
                    style={{ cursor: 'pointer' }}
                    onClick={clearRecentSearches}
                  >
                    清除
                  </Text>
                </div>
                <Space wrap>
                  {recentSearches.map((query, index) => (
                    <Tag
                      key={index}
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleRecentSearchClick(query)}
                    >
                      {query}
                    </Tag>
                  ))}
                </Space>
                <Divider />
              </>
            ) : null}
            
            <Text type="secondary">
              💡 提示：使用 Ctrl+K 快速打开搜索，支持搜索新闻、订阅、设置等内容
            </Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default GlobalSearch;
