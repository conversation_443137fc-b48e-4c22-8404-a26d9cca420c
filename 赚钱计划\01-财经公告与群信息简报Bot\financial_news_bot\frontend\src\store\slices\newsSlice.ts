import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { News, NewsSearchParams, PaginatedResponse } from '../../types';

interface NewsState {
  news: News[];
  currentNews: News | null;
  loading: boolean;
  error: string | null;
  searchParams: NewsSearchParams;
  pagination: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  bookmarkedNews: number[];
}

const initialState: NewsState = {
  news: [],
  currentNews: null,
  loading: false,
  error: null,
  searchParams: {
    page: 1,
    limit: 20,
    sort_by: 'published_at',
    sort_order: 'desc',
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 20,
    total_pages: 0,
    has_next: false,
    has_prev: false,
  },
  bookmarkedNews: [],
};

// 异步thunks
export const fetchNews = createAsyncThunk(
  'news/fetchNews',
  async (params: NewsSearchParams, { rejectWithValue }) => {
    try {
      // 这里调用API获取新闻列表
      // const response = await newsService.getNews(params);
      // return response;
      
      // 暂时返回模拟数据
      return {
        code: 200,
        message: '获取成功',
        data: {
          items: [],
          pagination: {
            total: 0,
            page: params.page || 1,
            limit: params.limit || 20,
            total_pages: 0,
            has_next: false,
            has_prev: false,
          },
        },
        timestamp: new Date().toISOString(),
      } as PaginatedResponse<News>;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      return rejectWithValue(axiosError.response?.data?.message || '获取新闻失败');
    }
  }
);

export const fetchNewsDetail = createAsyncThunk(
  'news/fetchNewsDetail',
  async (_id: number, { _rejectWithValue }) => {
    // 这里调用API获取新闻详情
    // try {
    //   const response = await newsService.getNewsDetail(id);
    //   return response;
    // } catch (error: unknown) {
    //   const axiosError = error as { response?: { data?: { message?: string } } };
    //   return rejectWithValue(axiosError.response?.data?.message || '获取新闻详情失败');
    // }

    // 暂时返回模拟数据
    return null;
  }
);

const newsSlice = createSlice({
  name: 'news',
  initialState,
  reducers: {
    setSearchParams: (state, action: PayloadAction<Partial<NewsSearchParams>>) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    addBookmark: (state, action: PayloadAction<number>) => {
      if (!state.bookmarkedNews.includes(action.payload)) {
        state.bookmarkedNews.push(action.payload);
      }
    },
    removeBookmark: (state, action: PayloadAction<number>) => {
      state.bookmarkedNews = state.bookmarkedNews.filter(id => id !== action.payload);
    },
    clearCurrentNews: (state) => {
      state.currentNews = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch news
    builder
      .addCase(fetchNews.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNews.fulfilled, (state, action) => {
        state.loading = false;
        state.news = action.payload.data.items;
        state.pagination = action.payload.data.pagination;
        state.error = null;
      })
      .addCase(fetchNews.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch news detail
    builder
      .addCase(fetchNewsDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNewsDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.currentNews = action.payload;
        state.error = null;
      })
      .addCase(fetchNewsDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSearchParams,
  clearError,
  addBookmark,
  removeBookmark,
  clearCurrentNews,
} = newsSlice.actions;

export default newsSlice.reducer;
