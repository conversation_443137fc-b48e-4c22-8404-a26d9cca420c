import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { LoadingOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

interface OAuthCallbackPageProps {}

const OAuthCallbackPage: React.FC<OAuthCallbackPageProps> = () => {
  const { provider } = useParams<{ provider: string }>();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    try {
      const code = searchParams.get('code');
      // const state = searchParams.get('state'); // 暂时未使用，保留备用
      const error = searchParams.get('error');

      if (error) {
        throw new Error(searchParams.get('error_description') || '授权失败');
      }

      if (!code) {
        throw new Error('未获取到授权码');
      }

      // 真实处理OAuth回调
      const userInfo = await fetchUserInfo(provider!, code);

      // 向父窗口发送成功消息
      if (window.opener) {
        window.opener.postMessage({
          type: 'SOCIAL_LOGIN_SUCCESS',
          userInfo: userInfo,
        }, window.location.origin);
        window.close();
      } else {
        setStatus('success');
        setMessage('授权成功，正在跳转...');
        // 立即跳转，无需延迟
        window.location.href = '/dashboard';
      }
    } catch (error: unknown) {
      // OAuth回调错误日志 - 用于调试第三方登录问题
      console.error('OAuth callback error:', error);
      
      if (window.opener) {
        window.opener.postMessage({
          type: 'SOCIAL_LOGIN_ERROR',
          error: (error as Error).message,
        }, window.location.origin);
        window.close();
      } else {
        setStatus('error');
        setMessage(error.message || '授权处理失败');
      }
    }
  };

  const fetchUserInfo = async (provider: string, code: string) => {
    try {
      // 真实OAuth用户信息获取
      const response = await fetch(`/api/v1/auth/oauth/${provider}/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, state: searchParams.get('state') }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.user_info;
      } else {
        throw new Error('获取用户信息失败');
      }
    } catch (error) {
      // 获取用户信息失败日志 - 用于调试第三方登录问题
      console.error('获取用户信息失败:', error);
      throw error;
    }
  };

  const getProviderName = (provider: string) => {
    const names = {
      qq: 'QQ',
      github: 'GitHub',
      google: 'Google',
      alipay: '支付宝',
      wechat: '微信',
    };
    return names[provider as keyof typeof names] || provider;
  };

  if (status === 'loading') {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column'
      }}>
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
          size="large"
        />
        <div style={{ marginTop: '24px', fontSize: '16px', color: '#666' }}>
          正在处理{getProviderName(provider || '')}授权...
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <Result
        icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
        title="授权成功"
        subTitle={message}
        extra={
          <Button type="primary" onClick={() => window.location.href = '/dashboard'}>
            进入应用
          </Button>
        }
      />
    );
  }

  if (status === 'error') {
    return (
      <Result
        icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
        title="授权失败"
        subTitle={message}
        extra={[
          <Button key="retry" onClick={() => window.location.href = '/auth/login'}>
            重新登录
          </Button>,
          <Button key="home" type="primary" onClick={() => window.location.href = '/'}>
            返回首页
          </Button>,
        ]}
      />
    );
  }

  return null;
};

export default OAuthCallbackPage;
