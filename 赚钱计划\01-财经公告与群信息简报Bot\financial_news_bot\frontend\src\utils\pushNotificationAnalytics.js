/**
 * 客户端推送通知统计和效果分析
 * 统计浏览器/移动端通知的发送与到达、点击与转化
 * 分析客户端通知偏好和行为模式，进行A/B测试优化
 */

class PushNotificationAnalytics {
    constructor() {
        this.apiBase = '/api/v1/analytics/push';
        this.storage = window.localStorage;
        this.sessionStorage = window.sessionStorage;
        this.userId = this.getUserId();
        this.sessionId = this.generateSessionId();
        
        // 初始化事件监听
        this.initEventListeners();
        
        // A/B测试配置
        this.abTestConfig = null;
        this.loadABTestConfig();
    }

    /**
     * 获取用户ID
     */
    getUserId() {
        const token = this.storage.getItem('auth_token');
        if (token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                return payload.sub || payload.user_id;
            } catch (e) {
                // 无法解析用户ID警告 - 用于调试token解析问题（仅开发环境）
                if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
                    console.warn('无法解析用户ID:', e);
                }
            }
        }
        
        // 如果没有登录，使用设备指纹
        let deviceId = this.storage.getItem('device_id');
        if (!deviceId) {
            deviceId = this.generateDeviceId();
            this.storage.setItem('device_id', deviceId);
        }
        return deviceId;
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        let sessionId = this.sessionStorage.getItem('session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            this.sessionStorage.setItem('session_id', sessionId);
        }
        return sessionId;
    }

    /**
     * 生成设备指纹ID
     */
    generateDeviceId() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        return 'device_' + btoa(fingerprint).substr(0, 16);
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.trackEvent('page_visible', {
                    timestamp: Date.now(),
                    from_notification: this.sessionStorage.getItem('from_notification') === 'true'
                });
            }
        });

        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.flushPendingEvents();
        });

        // 监听通知权限变化
        if ('Notification' in window) {
            // 定期检查权限状态
            setInterval(() => {
                this.checkNotificationPermission();
            }, 30000);
        }
    }

    /**
     * 请求通知权限并统计
     */
    async requestNotificationPermission() {
        if (!('Notification' in window)) {
            this.trackEvent('notification_not_supported', {
                userAgent: navigator.userAgent,
                timestamp: Date.now()
            });
            return 'not-supported';
        }

        const startTime = Date.now();
        let permission;

        try {
            permission = await Notification.requestPermission();
        } catch {
            permission = Notification.permission;
        }

        this.trackEvent('notification_permission_request', {
            result: permission,
            responseTime: Date.now() - startTime,
            timestamp: Date.now()
        });

        return permission;
    }

    /**
     * 检查通知权限状态
     */
    checkNotificationPermission() {
        if ('Notification' in window) {
            const permission = Notification.permission;
            const lastPermission = this.storage.getItem('last_notification_permission');
            
            if (lastPermission !== permission) {
                this.trackEvent('notification_permission_changed', {
                    from: lastPermission,
                    to: permission,
                    timestamp: Date.now()
                });
                this.storage.setItem('last_notification_permission', permission);
            }
        }
    }

    /**
     * 显示通知并统计
     */
    async showNotification(title, options = {}) {
        const notificationId = 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // 记录通知发送事件
        this.trackEvent('notification_sent', {
            notificationId,
            title,
            hasIcon: !!options.icon,
            hasImage: !!options.image,
            hasActions: !!(options.actions && options.actions.length > 0),
            tag: options.tag,
            timestamp: Date.now()
        });

        try {
            // 检查权限
            if (Notification.permission !== 'granted') {
                this.trackEvent('notification_blocked', {
                    notificationId,
                    reason: 'permission_denied',
                    permission: Notification.permission,
                    timestamp: Date.now()
                });
                return null;
            }

            // 创建通知
            const notification = new Notification(title, {
                ...options,
                data: {
                    ...options.data,
                    notificationId,
                    sentAt: Date.now()
                }
            });

            // 监听通知事件
            notification.onshow = () => {
                this.trackEvent('notification_displayed', {
                    notificationId,
                    timestamp: Date.now()
                });
            };

            notification.onclick = () => {
                this.trackEvent('notification_clicked', {
                    notificationId,
                    timestamp: Date.now(),
                    clickTarget: 'body'
                });
                
                // 标记来源
                this.sessionStorage.setItem('from_notification', 'true');
                this.sessionStorage.setItem('notification_id', notificationId);
            };

            notification.onclose = () => {
                this.trackEvent('notification_closed', {
                    notificationId,
                    timestamp: Date.now()
                });
            };

            notification.onerror = (error) => {
                this.trackEvent('notification_error', {
                    notificationId,
                    error: error.message || 'Unknown error',
                    timestamp: Date.now()
                });
            };

            return notification;

        } catch (error) {
            this.trackEvent('notification_creation_failed', {
                notificationId,
                error: error.message,
                timestamp: Date.now()
            });
            return null;
        }
    }

    /**
     * 统计Service Worker推送通知
     */
    trackServiceWorkerNotification(payload) {
        const notificationId = payload.notificationId || 'sw_' + Date.now();
        
        this.trackEvent('sw_notification_received', {
            notificationId,
            hasPayload: !!payload.data,
            timestamp: Date.now()
        });

        // 如果有点击数据，记录点击事件
        if (payload.action === 'click') {
            this.trackEvent('sw_notification_clicked', {
                notificationId,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 分析用户通知偏好
     */
    async analyzeUserPreferences() {
        const events = this.getPendingEvents();
        const analysis = {
            totalNotifications: 0,
            clickRate: 0,
            preferredTime: null,
            deviceType: this.getDeviceType(),
            engagementScore: 0
        };

        // 统计通知数据
        const sentNotifications = events.filter(e => e.type === 'notification_sent');
        const clickedNotifications = events.filter(e => e.type === 'notification_clicked');
        
        analysis.totalNotifications = sentNotifications.length;
        analysis.clickRate = sentNotifications.length > 0 ? 
            (clickedNotifications.length / sentNotifications.length) * 100 : 0;

        // 分析最佳推送时间
        if (clickedNotifications.length > 0) {
            const clickTimes = clickedNotifications.map(e => {
                const date = new Date(e.data.timestamp);
                return date.getHours();
            });
            
            // 找出点击最多的时间段
            const timeFreq = {};
            clickTimes.forEach(hour => {
                timeFreq[hour] = (timeFreq[hour] || 0) + 1;
            });
            
            analysis.preferredTime = Object.keys(timeFreq).reduce((a, b) => 
                timeFreq[a] > timeFreq[b] ? a : b
            );
        }

        // 计算参与度分数
        analysis.engagementScore = this.calculateEngagementScore(events);

        return analysis;
    }

    /**
     * 计算用户参与度分数
     */
    calculateEngagementScore(events) {
        let score = 0;
        
        // 基础分数：通知权限授予
        if (Notification.permission === 'granted') {
            score += 30;
        }

        // 点击率分数
        const sentCount = events.filter(e => e.type === 'notification_sent').length;
        const clickCount = events.filter(e => e.type === 'notification_clicked').length;
        
        if (sentCount > 0) {
            const clickRate = clickCount / sentCount;
            score += clickRate * 50; // 最高50分
        }

        // 活跃度分数
        const recentEvents = events.filter(e => 
            Date.now() - e.data.timestamp < 7 * 24 * 60 * 60 * 1000 // 最近7天
        );
        
        if (recentEvents.length > 10) {
            score += 20; // 活跃用户额外20分
        }

        return Math.min(100, Math.round(score));
    }

    /**
     * A/B测试功能
     */
    async loadABTestConfig() {
        try {
            const response = await fetch(`${this.apiBase}/ab-test-config`);
            if (response.ok) {
                this.abTestConfig = await response.json();
            }
        } catch (error) {
            // A/B测试配置加载失败警告 - 用于调试推送A/B测试问题（仅开发环境）
            if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
                console.warn('无法加载A/B测试配置:', error);
            }
        }
    }

    /**
     * 获取A/B测试变体
     */
    getABTestVariant(testName) {
        if (!this.abTestConfig || !this.abTestConfig[testName]) {
            return 'control';
        }

        const test = this.abTestConfig[testName];
        const userHash = this.hashUserId(this.userId + testName);
        const bucket = userHash % 100;

        let cumulative = 0;
        for (const variant of test.variants) {
            cumulative += variant.percentage;
            if (bucket < cumulative) {
                this.trackEvent('ab_test_assignment', {
                    testName,
                    variant: variant.name,
                    timestamp: Date.now()
                });
                return variant.name;
            }
        }

        return 'control';
    }

    /**
     * 哈希用户ID用于A/B测试
     */
    hashUserId(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }

    /**
     * 获取设备类型
     */
    getDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
            return 'mobile';
        } else if (/tablet|ipad/i.test(userAgent)) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    /**
     * 跟踪事件
     */
    trackEvent(type, data) {
        const event = {
            type,
            data: {
                ...data,
                userId: this.userId,
                sessionId: this.sessionId,
                userAgent: navigator.userAgent,
                url: window.location.href,
                referrer: document.referrer,
                deviceType: this.getDeviceType()
            },
            timestamp: Date.now()
        };

        // 存储到本地
        this.storePendingEvent(event);

        // 尝试立即发送
        this.sendEvent(event);
    }

    /**
     * 存储待发送事件
     */
    storePendingEvent(event) {
        const pending = this.getPendingEvents();
        pending.push(event);
        
        // 只保留最近1000个事件
        if (pending.length > 1000) {
            pending.splice(0, pending.length - 1000);
        }
        
        this.storage.setItem('pending_push_events', JSON.stringify(pending));
    }

    /**
     * 获取待发送事件
     */
    getPendingEvents() {
        try {
            const stored = this.storage.getItem('pending_push_events');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            // 待发送事件解析失败警告 - 用于调试本地存储问题（仅开发环境）
            if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
                console.warn('无法解析待发送事件:', error);
            }
            return [];
        }
    }

    /**
     * 发送事件到服务器
     */
    async sendEvent(event) {
        try {
            const response = await fetch(`${this.apiBase}/events`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.storage.getItem('auth_token')}`
                },
                body: JSON.stringify(event)
            });

            if (response.ok) {
                this.removePendingEvent(event);
            }
        } catch (error) {
            // 推送事件发送失败警告 - 用于监控推送分析上报问题（仅开发环境）
            if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
                console.warn('发送事件失败:', error);
            }
        }
    }

    /**
     * 移除已发送的事件
     */
    removePendingEvent(sentEvent) {
        const pending = this.getPendingEvents();
        const filtered = pending.filter(event => 
            event.timestamp !== sentEvent.timestamp || event.type !== sentEvent.type
        );
        this.storage.setItem('pending_push_events', JSON.stringify(filtered));
    }

    /**
     * 批量发送待发送事件
     */
    async flushPendingEvents() {
        const pending = this.getPendingEvents();
        if (pending.length === 0) return;

        try {
            const response = await fetch(`${this.apiBase}/events/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.storage.getItem('auth_token')}`
                },
                body: JSON.stringify({ events: pending })
            });

            if (response.ok) {
                this.storage.removeItem('pending_push_events');
            }
        } catch (error) {
            // 批量推送事件发送失败警告 - 用于监控批量上报问题（仅开发环境）
            if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
                console.warn('批量发送事件失败:', error);
            }
        }
    }

    /**
     * 生成推送效果报告
     */
    async generateReport(timeRange = '7d') {
        const events = this.getPendingEvents();
        const cutoffTime = Date.now() - this.getTimeRangeMs(timeRange);
        const recentEvents = events.filter(e => e.timestamp >= cutoffTime);

        const report = {
            timeRange,
            summary: {
                totalNotifications: 0,
                displayedNotifications: 0,
                clickedNotifications: 0,
                clickRate: 0,
                engagementScore: 0
            },
            breakdown: {
                byDeviceType: {},
                byTimeOfDay: {},
                byDayOfWeek: {}
            },
            abTests: {}
        };

        // 计算基础统计
        const sentEvents = recentEvents.filter(e => e.type === 'notification_sent');
        const displayedEvents = recentEvents.filter(e => e.type === 'notification_displayed');
        const clickedEvents = recentEvents.filter(e => e.type === 'notification_clicked');

        report.summary.totalNotifications = sentEvents.length;
        report.summary.displayedNotifications = displayedEvents.length;
        report.summary.clickedNotifications = clickedEvents.length;
        report.summary.clickRate = sentEvents.length > 0 ? 
            (clickedEvents.length / sentEvents.length) * 100 : 0;
        report.summary.engagementScore = this.calculateEngagementScore(recentEvents);

        // 按设备类型分析
        sentEvents.forEach(event => {
            const deviceType = event.data.deviceType;
            if (!report.breakdown.byDeviceType[deviceType]) {
                report.breakdown.byDeviceType[deviceType] = { sent: 0, clicked: 0 };
            }
            report.breakdown.byDeviceType[deviceType].sent++;
        });

        clickedEvents.forEach(event => {
            const deviceType = event.data.deviceType;
            if (report.breakdown.byDeviceType[deviceType]) {
                report.breakdown.byDeviceType[deviceType].clicked++;
            }
        });

        return report;
    }

    /**
     * 获取时间范围的毫秒数
     */
    getTimeRangeMs(timeRange) {
        const ranges = {
            '1d': 24 * 60 * 60 * 1000,
            '7d': 7 * 24 * 60 * 60 * 1000,
            '30d': 30 * 24 * 60 * 60 * 1000
        };
        return ranges[timeRange] || ranges['7d'];
    }
}

// 导出单例实例
const pushAnalytics = new PushNotificationAnalytics();

export default pushAnalytics;
