#!/usr/bin/env python3
"""
开发环境启动脚本
提供便捷的开发环境管理功能
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path
from typing import List, Dict, Optional

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

class DevEnvironment:
    """开发环境管理类"""
    
    def __init__(self):
        self.project_root = PROJECT_ROOT
        self.compose_file = self.project_root / "docker-compose.yml"
        
    def run_command(self, command: List[str], cwd: Optional[Path] = None) -> int:
        """运行命令"""
        if cwd is None:
            cwd = self.project_root
            
        print(f"🔧 执行命令: {' '.join(command)}")
        print(f"📁 工作目录: {cwd}")
        
        try:
            result = subprocess.run(command, cwd=cwd, check=True)
            return result.returncode
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            return e.returncode
        except FileNotFoundError:
            print(f"❌ 命令未找到: {command[0]}")
            return 1
    
    def check_docker(self) -> bool:
        """检查Docker是否可用"""
        try:
            subprocess.run(["docker", "--version"], capture_output=True, check=True)
            subprocess.run(["docker-compose", "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Docker或Docker Compose未安装或不可用")
            return False
    
    def validate_env(self) -> bool:
        """验证环境变量"""
        print("🔍 验证环境变量配置...")
        return self.run_command([sys.executable, "scripts/validate_env.py"]) == 0
    
    def build_services(self, services: Optional[List[str]] = None) -> bool:
        """构建服务"""
        print("🏗️ 构建Docker服务...")
        command = ["docker-compose", "build"]
        if services:
            command.extend(services)
        return self.run_command(command) == 0
    
    def start_services(self, services: Optional[List[str]] = None, detached: bool = True) -> bool:
        """启动服务"""
        print("🚀 启动服务...")
        command = ["docker-compose", "up"]
        if detached:
            command.append("-d")
        if services:
            command.extend(services)
        return self.run_command(command) == 0
    
    def stop_services(self) -> bool:
        """停止服务"""
        print("🛑 停止服务...")
        return self.run_command(["docker-compose", "down"]) == 0
    
    def restart_services(self, services: Optional[List[str]] = None) -> bool:
        """重启服务"""
        print("🔄 重启服务...")
        command = ["docker-compose", "restart"]
        if services:
            command.extend(services)
        return self.run_command(command) == 0
    
    def show_logs(self, services: Optional[List[str]] = None, follow: bool = False) -> bool:
        """显示日志"""
        print("📋 显示服务日志...")
        command = ["docker-compose", "logs"]
        if follow:
            command.append("-f")
        if services:
            command.extend(services)
        return self.run_command(command) == 0
    
    def show_status(self) -> bool:
        """显示服务状态"""
        print("📊 服务状态:")
        return self.run_command(["docker-compose", "ps"]) == 0
    
    def run_migrations(self) -> bool:
        """运行数据库迁移"""
        print("🗄️ 执行数据库迁移...")
        return self.run_command([
            "docker-compose", "exec", "backend", 
            "alembic", "upgrade", "head"
        ]) == 0
    
    def create_admin_user(self, username: str = "admin", email: str = "<EMAIL>", password: str = "admin123") -> bool:
        """创建管理员用户"""
        print(f"👤 创建管理员用户: {username}")
        python_code = f"""
from app.services.user import UserService
from app.database import get_db
from app.models.user import UserRole

db = next(get_db())
user_service = UserService(db)

try:
    user = user_service.create_user(
        username='{username}',
        email='{email}',
        password='{password}',
        role=UserRole.ADMIN
    )
    print(f'管理员用户创建成功: {{user.username}} ({{user.email}})')
except Exception as e:
    print(f'创建用户失败: {{e}}')
"""
        return self.run_command([
            "docker-compose", "exec", "backend",
            "python", "-c", python_code
        ]) == 0
    
    def run_tests(self, test_type: str = "all") -> bool:
        """运行测试"""
        print(f"🧪 运行测试: {test_type}")
        
        if test_type == "backend" or test_type == "all":
            print("🐍 运行后端测试...")
            result = self.run_command([
                "docker-compose", "exec", "backend",
                "pytest", "-v"
            ])
            if result != 0:
                return False
        
        if test_type == "frontend" or test_type == "all":
            print("⚛️ 运行前端测试...")
            result = self.run_command([
                "docker-compose", "exec", "frontend",
                "npm", "test", "--", "--watchAll=false"
            ])
            if result != 0:
                return False
        
        return True
    
    def health_check(self) -> bool:
        """健康检查"""
        print("🏥 执行健康检查...")
        
        # 检查服务状态
        if not self.show_status():
            return False
        
        # 检查API健康
        try:
            import requests
            response = requests.get("http://localhost:8080/health", timeout=10)
            if response.status_code == 200:
                print("✅ API健康检查通过")
            else:
                print(f"❌ API健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API健康检查失败: {e}")
            return False
        
        return True
    
    def full_setup(self) -> bool:
        """完整环境设置"""
        print("🎯 开始完整环境设置...")
        
        # 1. 检查Docker
        if not self.check_docker():
            return False
        
        # 2. 验证环境变量
        if not self.validate_env():
            return False
        
        # 3. 构建服务
        if not self.build_services():
            return False
        
        # 4. 启动服务
        if not self.start_services():
            return False
        
        # 5. 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(30)
        
        # 6. 运行数据库迁移
        if not self.run_migrations():
            return False
        
        # 7. 创建管理员用户
        if not self.create_admin_user():
            print("⚠️ 管理员用户创建失败，可能已存在")
        
        # 8. 健康检查
        if not self.health_check():
            return False
        
        print("🎉 环境设置完成！")
        print("🌐 访问地址:")
        print("  - 前端应用: http://localhost:8080")
        print("  - API文档: http://localhost:8080/docs")
        print("  - 健康检查: http://localhost:8080/health")
        print("👤 管理员账户: admin / admin123")
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="财经新闻Bot开发环境管理")
    parser.add_argument("action", choices=[
        "setup", "start", "stop", "restart", "status", "logs", 
        "build", "migrate", "test", "health", "admin"
    ], help="要执行的操作")
    parser.add_argument("--services", nargs="*", help="指定服务名称")
    parser.add_argument("--follow", "-f", action="store_true", help="跟踪日志")
    parser.add_argument("--test-type", choices=["all", "backend", "frontend"], 
                       default="all", help="测试类型")
    
    args = parser.parse_args()
    
    dev_env = DevEnvironment()
    
    try:
        if args.action == "setup":
            success = dev_env.full_setup()
        elif args.action == "start":
            success = dev_env.start_services(args.services)
        elif args.action == "stop":
            success = dev_env.stop_services()
        elif args.action == "restart":
            success = dev_env.restart_services(args.services)
        elif args.action == "status":
            success = dev_env.show_status()
        elif args.action == "logs":
            success = dev_env.show_logs(args.services, args.follow)
        elif args.action == "build":
            success = dev_env.build_services(args.services)
        elif args.action == "migrate":
            success = dev_env.run_migrations()
        elif args.action == "test":
            success = dev_env.run_tests(args.test_type)
        elif args.action == "health":
            success = dev_env.health_check()
        elif args.action == "admin":
            success = dev_env.create_admin_user()
        else:
            print(f"❌ 未知操作: {args.action}")
            success = False
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
