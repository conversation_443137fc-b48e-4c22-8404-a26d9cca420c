import js from '@eslint/js';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';

export default [
  // 基础JavaScript配置
  js.configs.recommended,
  
  // TypeScript文件配置
  {
    files: ['**/*.{ts,tsx,js,jsx}'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2024,
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Node.js globals
        console: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly',
        global: 'readonly',

        // Browser globals
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        Navigator: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        fetch: 'readonly',

        // Timer functions
        setTimeout: 'readonly',
        setInterval: 'readonly',
        clearTimeout: 'readonly',
        clearInterval: 'readonly',
        requestIdleCallback: 'readonly',

        // DOM and Web APIs
        HTMLElement: 'readonly',
        HTMLDivElement: 'readonly',
        HTMLImageElement: 'readonly',
        HTMLFormElement: 'readonly',
        HTMLInputElement: 'readonly',
        HTMLTextAreaElement: 'readonly',
        FormData: 'readonly',
        File: 'readonly',
        Blob: 'readonly',
        URL: 'readonly',
        URLSearchParams: 'readonly',
        Image: 'readonly',

        // Events
        Event: 'readonly',
        CustomEvent: 'readonly',
        TouchEvent: 'readonly',
        MessageEvent: 'readonly',
        ErrorEvent: 'readonly',
        PromiseRejectionEvent: 'readonly',
        KeyboardEvent: 'readonly',

        // Performance and Observers
        performance: 'readonly',
        Performance: 'readonly',
        PerformanceObserver: 'readonly',
        PerformanceEntry: 'readonly',
        PerformanceNavigationTiming: 'readonly',
        PerformanceResourceTiming: 'readonly',
        IntersectionObserver: 'readonly',
        MutationObserver: 'readonly',

        // Notifications
        Notification: 'readonly',

        // Encoding/Decoding
        atob: 'readonly',
        btoa: 'readonly',

        // Screen
        screen: 'readonly',

        // React (for JSX)
        React: 'readonly',

        // Testing globals
        jest: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly',

        // Node.js types
        NodeJS: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': typescript,
    },
    rules: {
      // 基本规则
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      'prefer-const': 'warn',
      'no-undef': 'warn',
      'no-redeclare': 'warn',
      'no-unreachable': 'warn',
      
      // TypeScript规则
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-empty-function': 'warn',
    },
  },

  // 忽略文件
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'coverage/**',
      '*.min.js',
      'backend/alembic/**',
      'backend/__pycache__/**',
      '.git/**',
      '.env',
      'docker-compose.yml',
      '*.md',
      '*.json',
      '*.ini',
      '*.cfg',
      '*.toml',
      '*.yml',
      '*.yaml',
    ],
  },
];