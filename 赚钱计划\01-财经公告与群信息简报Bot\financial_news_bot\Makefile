# 财经新闻Bot - 开发环境管理 Makefile
# 提供便捷的开发命令

.PHONY: help setup start stop restart status logs build migrate test health admin clean

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[34m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

help: ## 显示帮助信息
	@echo "$(BLUE)财经新闻Bot - 开发环境管理$(RESET)"
	@echo "=================================="
	@echo ""
	@echo "$(GREEN)可用命令:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-12s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)访问地址:$(RESET)"
	@echo "  前端应用: http://localhost:8080"
	@echo "  API文档:  http://localhost:8080/docs"
	@echo "  健康检查: http://localhost:8080/health"

setup: ## 完整环境设置（推荐首次使用）
	@echo "$(BLUE)🎯 开始完整环境设置...$(RESET)"
	@python scripts/dev-start.py setup

start: ## 启动所有服务
	@echo "$(BLUE)🚀 启动服务...$(RESET)"
	@docker-compose up -d

stop: ## 停止所有服务
	@echo "$(BLUE)🛑 停止服务...$(RESET)"
	@docker-compose down

restart: ## 重启所有服务
	@echo "$(BLUE)🔄 重启服务...$(RESET)"
	@docker-compose restart

status: ## 显示服务状态
	@echo "$(BLUE)📊 服务状态:$(RESET)"
	@docker-compose ps

logs: ## 显示服务日志
	@echo "$(BLUE)📋 显示服务日志...$(RESET)"
	@docker-compose logs

logs-f: ## 跟踪服务日志
	@echo "$(BLUE)📋 跟踪服务日志...$(RESET)"
	@docker-compose logs -f

build: ## 构建Docker镜像
	@echo "$(BLUE)🏗️ 构建Docker服务...$(RESET)"
	@docker-compose build

migrate: ## 执行数据库迁移
	@echo "$(BLUE)🗄️ 执行数据库迁移...$(RESET)"
	@docker-compose exec backend alembic upgrade head

test: ## 运行所有测试
	@echo "$(BLUE)🧪 运行测试...$(RESET)"
	@python scripts/dev-start.py test

test-backend: ## 运行后端测试
	@echo "$(BLUE)🐍 运行后端测试...$(RESET)"
	@docker-compose exec backend pytest -v

test-frontend: ## 运行前端测试
	@echo "$(BLUE)⚛️ 运行前端测试...$(RESET)"
	@docker-compose exec frontend npm test -- --watchAll=false

health: ## 执行健康检查
	@echo "$(BLUE)🏥 执行健康检查...$(RESET)"
	@python scripts/dev-start.py health

admin: ## 创建管理员用户
	@echo "$(BLUE)👤 创建管理员用户...$(RESET)"
	@python scripts/dev-start.py admin

validate: ## 验证环境配置
	@echo "$(BLUE)🔍 验证环境配置...$(RESET)"
	@python scripts/validate_env.py

clean: ## 清理Docker资源
	@echo "$(BLUE)🧹 清理Docker资源...$(RESET)"
	@docker-compose down -v --remove-orphans
	@docker system prune -f

# 开发相关命令
dev-backend: ## 启动后端开发模式
	@echo "$(BLUE)🐍 启动后端开发模式...$(RESET)"
	@cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-frontend: ## 启动前端开发模式
	@echo "$(BLUE)⚛️ 启动前端开发模式...$(RESET)"
	@cd frontend && npm start

# 数据库相关命令
db-shell: ## 连接数据库Shell
	@echo "$(BLUE)🗄️ 连接数据库Shell...$(RESET)"
	@docker-compose exec mysql mysql -u velen -p financial_news_bot

db-backup: ## 备份数据库
	@echo "$(BLUE)💾 备份数据库...$(RESET)"
	@docker-compose exec mysql mysqldump -u velen -p financial_news_bot > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 日志相关命令
logs-backend: ## 查看后端日志
	@docker-compose logs -f backend

logs-frontend: ## 查看前端日志
	@docker-compose logs -f frontend

logs-mysql: ## 查看MySQL日志
	@docker-compose logs -f mysql

logs-redis: ## 查看Redis日志
	@docker-compose logs -f redis

# 服务管理命令
restart-backend: ## 重启后端服务
	@docker-compose restart backend

restart-frontend: ## 重启前端服务
	@docker-compose restart frontend

restart-mysql: ## 重启MySQL服务
	@docker-compose restart mysql

restart-redis: ## 重启Redis服务
	@docker-compose restart redis

# 代码质量检查
lint: ## 代码质量检查
	@echo "$(BLUE)🔍 代码质量检查...$(RESET)"
	@echo "检查后端代码..."
	@docker-compose exec backend flake8 app/
	@echo "检查前端代码..."
	@docker-compose exec frontend npm run lint

format: ## 代码格式化
	@echo "$(BLUE)✨ 代码格式化...$(RESET)"
	@echo "格式化后端代码..."
	@docker-compose exec backend black app/
	@echo "格式化前端代码..."
	@docker-compose exec frontend npm run format

# 安全检查
security: ## 安全检查
	@echo "$(BLUE)🔒 安全检查...$(RESET)"
	@python scripts/validate_env.py

# 性能测试
perf: ## 性能测试
	@echo "$(BLUE)⚡ 性能测试...$(RESET)"
	@echo "暂未实现，请手动运行性能测试工具"

# 完整的CI/CD流程
ci: validate build test lint security ## 完整的CI检查流程
	@echo "$(GREEN)✅ CI检查完成$(RESET)"

# 快速重置环境
reset: clean setup ## 重置整个环境
	@echo "$(GREEN)🔄 环境重置完成$(RESET)"
