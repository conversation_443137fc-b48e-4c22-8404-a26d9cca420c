{"name": "financial-news-bot", "version": "1.0.0", "description": "财经新闻Bot项目 - 统一lint配置", "type": "module", "scripts": {"lint": "npm run lint:js && npm run lint:python", "lint:js": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:python": "python -m flake8 backend/", "lint:report": "npm run lint:js:report && npm run lint:python:report", "lint:js:report": "npx eslint . --ext .js,.jsx,.ts,.tsx --format unix > eslint-report.txt 2>&1 & exit 0", "lint:python:report": "python -m flake8 backend/ --format=default > flake8-report.txt 2>&1 & exit 0"}, "devDependencies": {"eslint": "^9.34.0", "@eslint/js": "^9.34.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.0"}}