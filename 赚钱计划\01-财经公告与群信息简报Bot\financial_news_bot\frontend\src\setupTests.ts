/**
 * Jest测试设置文件
 * 配置测试环境和全局设置
 */
import '@testing-library/jest-dom';

// 声明全局类型
declare global {
  // 声明Storage类型
  interface Storage {
    readonly length: number;
    clear(): void;
    getItem(_key: string): string | null;
    key(_index: number): string | null;
    removeItem(_key: string): void;
    setItem(_key: string, _value: string): void;
  }

  interface Window {
    Storage: {
      new(): Storage;
      prototype: Storage;
    };
  }
}

// 模拟环境变量
process.env.REACT_APP_API_BASE_URL = 'http://localhost:8000';
process.env.REACT_APP_VERSION = '1.0.0';
process.env.NODE_ENV = 'test';

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
} as typeof window.localStorage;
global.localStorage = localStorageMock;

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
} as typeof window.sessionStorage;
global.sessionStorage = sessionStorageMock;

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {
    // 测试环境下的空实现
  }
  observe(): void {
    // 测试环境下的空实现
  }
  disconnect(): void {
    // 测试环境下的空实现
  }
  unobserve(): void {
    // 测试环境下的空实现
  }
};

// 模拟ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {
    // 测试环境下的空实现
  }
  observe(): void {
    // 测试环境下的空实现
  }
  disconnect(): void {
    // 测试环境下的空实现
  }
  unobserve(): void {
    // 测试环境下的空实现
  }
};

// 模拟console方法（避免测试时输出过多日志）
const originalError = console.error;
beforeAll(() => {
  // 测试环境console过滤 - 用于减少测试输出噪音
  console.error = (...args: unknown[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  // 测试环境console恢复 - 用于清理测试环境
  console.error = originalError;
});

// 全局测试工具函数
global.testUtils = {
  // 等待异步操作完成
  waitFor: (_ms: number) => new Promise<void>(resolve => setTimeout(resolve, _ms)),

  // 模拟API响应
  mockApiResponse: (_data: unknown, _status = 200) => ({
    data: _data,
    status: _status,
    statusText: 'OK',
    headers: {},
    config: {},
  }),

  // 模拟错误响应
  mockApiError: (_message: string, _status = 500) => ({
    response: {
      data: { message: _message },
      status: _status,
      statusText: 'Internal Server Error',
    },
  }),
};

// 类型声明
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        waitFor: (_ms: number) => Promise<void>;
        mockApiResponse: (_data: unknown, _status?: number) => unknown;
        mockApiError: (_message: string, _status?: number) => unknown;
      };
    }
  }
}
