{"name": "financial-news-bot-frontend", "version": "1.0.0", "description": "财经公告与群信息简报Bot前端应用", "private": true, "dependencies": {"@ant-design/icons": "^5.6.0", "@reduxjs/toolkit": "^2.5.0", "@types/node": "^22.10.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "antd": "^5.23.0", "axios": "^1.7.0", "dayjs": "^1.11.13", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.0", "react-scripts": "5.0.1", "typescript": "^5.7.0", "web-vitals": "^5.1.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^9.18.0", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0", "prettier": "^3.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss,json}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000", "_comment": "代理配置：开发环境使用localhost:8000，生产环境通过环境变量REACT_APP_API_URL配置"}