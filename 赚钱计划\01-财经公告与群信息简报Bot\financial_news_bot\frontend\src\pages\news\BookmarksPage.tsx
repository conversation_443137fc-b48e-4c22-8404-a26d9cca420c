import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Tag,
  Space,
  Typography,
  Input,
  Modal,
  Form,
  message,
  Dropdown,
  Empty,
  Row,
  Col,
} from 'antd';
import {
  FolderOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ExportOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';

const { Title, Text } = Typography;
const { Search } = Input;

interface BookmarkFolder {
  id: string;
  name: string;
  description: string;
  count: number;
  created_at: string;
}

interface BookmarkedNews {
  id: number;
  title: string;
  summary: string;
  source: string;
  published_at: string;
  bookmarked_at: string;
  folder_id: string;
  tags: string[];
  importance: 'high' | 'medium' | 'low';
}

const BookmarksPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  
  const [folders, setFolders] = useState<BookmarkFolder[]>([]);
  const [bookmarks, setBookmarks] = useState<BookmarkedNews[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [folderModalVisible, setFolderModalVisible] = useState(false);
  const [editingFolder, setEditingFolder] = useState<BookmarkFolder | null>(null);

  useEffect(() => {
    dispatch(setPageTitle('我的收藏'));
    loadFolders();
    loadBookmarks();
  }, [dispatch]);

  // 真实数据状态（移除所有模拟数据）
  const [, setFoldersLoading] = useState(true);
  const [, setBookmarksLoading] = useState(true);

  const loadFolders = async () => {
    try {
      setFoldersLoading(true);
      const response = await fetch('/api/v1/bookmarks/folders', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFolders(data);
      } else {
        message.error('获取收藏夹失败');
        setFolders([]);
      }
    } catch (error) {
      // 收藏夹加载错误日志 - 用于调试和监控
      console.error('加载收藏夹失败:', error);
      message.error('加载收藏夹失败，请稍后重试');
      setFolders([]);
    } finally {
      setFoldersLoading(false);
    }
  };

  const loadBookmarks = async () => {
    try {
      setBookmarksLoading(true);
      const params = new URLSearchParams({
        ...(selectedFolder && { folder_id: selectedFolder }),
        ...(searchKeyword && { search: searchKeyword }),
      });

      const response = await fetch(`/api/v1/bookmarks?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBookmarks(data.items || []);
      } else {
        message.error('获取收藏失败');
        setBookmarks([]);
      }
    } catch (error) {
      // 收藏数据加载错误日志 - 用于调试和监控
      console.error('加载收藏失败:', error);
      message.error('加载收藏失败，请稍后重试');
      setBookmarks([]);
    } finally {
      setBookmarksLoading(false);
    }
  };

  const handleCreateFolder = async (values: { name: string; description?: string }) => {
    const newFolder: BookmarkFolder = {
      id: Date.now().toString(),
      name: values.name,
      description: values.description || '',
      count: 0,
      created_at: new Date().toISOString(),
    };
    
    setFolders(prev => [...prev, newFolder]);
    setFolderModalVisible(false);
    form.resetFields();
    message.success('文件夹创建成功');
  };

  const handleEditFolder = (folder: BookmarkFolder) => {
    setEditingFolder(folder);
    form.setFieldsValue(folder);
    setFolderModalVisible(true);
  };

  const handleUpdateFolder = async (values: { name: string; description?: string }) => {
    if (!editingFolder) return;
    
    setFolders(prev => prev.map(folder =>
      folder.id === editingFolder.id
        ? { ...folder, ...values }
        : folder
    ));
    
    setFolderModalVisible(false);
    setEditingFolder(null);
    form.resetFields();
    message.success('文件夹更新成功');
  };

  const handleDeleteFolder = (folderId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除文件夹后，其中的收藏将移动到默认分类。确认删除吗？',
      onOk: () => {
        setFolders(prev => prev.filter(folder => folder.id !== folderId));
        message.success('文件夹删除成功');
      },
    });
  };

  const handleRemoveBookmark = (bookmarkId: number) => {
    setBookmarks(prev => prev.filter(bookmark => bookmark.id !== bookmarkId));
    message.success('收藏已移除');
  };

  const handleMoveBookmark = (bookmarkId: number, folderId: string) => {
    setBookmarks(prev => prev.map(bookmark =>
      bookmark.id === bookmarkId
        ? { ...bookmark, folder_id: folderId }
        : bookmark
    ));
    message.success('收藏已移动');
  };

  const handleExportBookmarks = () => {
    const exportData = filteredBookmarks.map(bookmark => ({
      标题: bookmark.title,
      摘要: bookmark.summary,
      来源: bookmark.source,
      发布时间: dayjs(bookmark.published_at).format('YYYY-MM-DD HH:mm'),
      收藏时间: dayjs(bookmark.bookmarked_at).format('YYYY-MM-DD HH:mm'),
      标签: bookmark.tags.join(', '),
    }));
    // TODO: 实现实际的导出功能 - 使用exportData进行CSV/Excel导出
    // 临时使用console.log展示导出数据结构，实际实现时将替换为文件下载
    if (process.env.NODE_ENV === 'development') {
      console.log('导出数据结构:', exportData.slice(0, 2)); // 仅显示前2条作为示例
    }
    message.success('收藏导出成功');
  };

  const filteredBookmarks = bookmarks.filter(bookmark => {
    const matchesFolder = selectedFolder === 'all' || bookmark.folder_id === selectedFolder;
    const matchesSearch = !searchKeyword || 
      bookmark.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      bookmark.summary.toLowerCase().includes(searchKeyword.toLowerCase());
    return matchesFolder && matchesSearch;
  });

  const getFolderName = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    return folder?.name || '未分类';
  };

  const renderBookmarkActions = (bookmark: BookmarkedNews) => [
    <Button
      type="text"
      icon={<ShareAltOutlined />}
      onClick={() => {
        navigator.clipboard.writeText(`${bookmark.title} - ${window.location.origin}/news/${bookmark.id}`);
        message.success('链接已复制到剪贴板');
      }}
    >
      分享
    </Button>,
    <Dropdown
      menu={{
        items: folders.map(folder => ({
          key: folder.id,
          label: folder.name,
          onClick: () => handleMoveBookmark(bookmark.id, folder.id),
        })),
      }}
    >
      <Button type="text" icon={<FolderOutlined />}>
        移动到
      </Button>
    </Dropdown>,
    <Button
      type="text"
      icon={<DeleteOutlined />}
      danger
      onClick={() => handleRemoveBookmark(bookmark.id)}
    >
      移除
    </Button>,
  ];

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ marginBottom: '16px' }}>
          我的收藏
        </Title>
        
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索收藏的新闻..."
              allowClear
              onSearch={setSearchKeyword}
              style={{ maxWidth: '400px' }}
            />
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setFolderModalVisible(true)}
              >
                新建文件夹
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExportBookmarks}
              >
                导出收藏
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Row gutter={24}>
        {/* 左侧文件夹列表 */}
        <Col xs={24} lg={6}>
          <Card title="收藏文件夹" size="small">
            <div
              style={{
                padding: '8px 12px',
                cursor: 'pointer',
                backgroundColor: selectedFolder === 'all' ? '#f0f0f0' : 'transparent',
                borderRadius: '4px',
                marginBottom: '8px',
              }}
              onClick={() => setSelectedFolder('all')}
            >
              <Space>
                <FolderOutlined />
                <Text>全部收藏</Text>
                <Text type="secondary">({bookmarks.length})</Text>
              </Space>
            </div>
            
            {folders.map(folder => (
              <div
                key={folder.id}
                style={{
                  padding: '8px 12px',
                  cursor: 'pointer',
                  backgroundColor: selectedFolder === folder.id ? '#f0f0f0' : 'transparent',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
                onClick={() => setSelectedFolder(folder.id)}
              >
                <Space>
                  <FolderOutlined />
                  <Text>{folder.name}</Text>
                  <Text type="secondary">({folder.count})</Text>
                </Space>
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'edit',
                        label: '编辑',
                        icon: <EditOutlined />,
                        onClick: (e) => {
                          e.domEvent.stopPropagation();
                          handleEditFolder(folder);
                        },
                      },
                      {
                        key: 'delete',
                        label: '删除',
                        icon: <DeleteOutlined />,
                        danger: true,
                        onClick: (e) => {
                          e.domEvent.stopPropagation();
                          handleDeleteFolder(folder.id);
                        },
                      },
                    ],
                  }}
                  trigger={['click']}
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<MoreOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  />
                </Dropdown>
              </div>
            ))}
          </Card>
        </Col>

        {/* 右侧收藏列表 */}
        <Col xs={24} lg={18}>
          <Card
            title={
              <Space>
                <Text>
                  {selectedFolder === 'all' ? '全部收藏' : getFolderName(selectedFolder)}
                </Text>
                <Text type="secondary">({filteredBookmarks.length})</Text>
              </Space>
            }
            size="small"
          >
            {filteredBookmarks.length === 0 ? (
              <Empty description="暂无收藏" />
            ) : (
              <List
                itemLayout="vertical"
                dataSource={filteredBookmarks}
                renderItem={(bookmark) => (
                  <List.Item
                    actions={renderBookmarkActions(bookmark)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <a href={`/news/${bookmark.id}`} target="_blank" rel="noopener noreferrer">
                            {bookmark.title}
                          </a>
                          <Tag color={bookmark.importance === 'high' ? 'red' : 'blue'}>
                            {bookmark.importance === 'high' ? '重要' : '一般'}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Text>{bookmark.summary}</Text>
                          <br />
                          <Space style={{ marginTop: '8px' }}>
                            <Text type="secondary">来源：{bookmark.source}</Text>
                            <Text type="secondary">
                              收藏时间：{dayjs(bookmark.bookmarked_at).format('MM-DD HH:mm')}
                            </Text>
                          </Space>
                          <div style={{ marginTop: '8px' }}>
                            <Space wrap>
                              {bookmark.tags.map(tag => (
                                <Tag key={tag}>{tag}</Tag>
                              ))}
                            </Space>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* 文件夹创建/编辑模态框 */}
      <Modal
        title={editingFolder ? '编辑文件夹' : '新建文件夹'}
        open={folderModalVisible}
        onCancel={() => {
          setFolderModalVisible(false);
          setEditingFolder(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingFolder ? handleUpdateFolder : handleCreateFolder}
        >
          <Form.Item
            name="name"
            label="文件夹名称"
            rules={[
              { required: true, message: '请输入文件夹名称' },
              { max: 20, message: '名称不能超过20个字符' },
            ]}
          >
            <Input placeholder="请输入文件夹名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ max: 100, message: '描述不能超过100个字符' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入文件夹描述（可选）"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingFolder ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setFolderModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BookmarksPage;
