import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { App as AntdApp, ConfigProvider, theme } from 'antd';
import { useAppDispatch, useAppSelector } from './store';
import { getCurrentUser } from './store/slices/authSlice';
import { initializeTheme, setIsMobile } from './store/slices/uiSlice';

// 性能监控
import { performanceMonitor } from './utils/performanceMonitor';
import { initializePreloading } from './utils/lazyLoading';

// 布局组件
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';

// 页面组件
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import OAuthCallbackPage from './pages/auth/OAuthCallbackPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import NewsPage from './pages/news/NewsPage';
import NewsDetailPage from './pages/news/NewsDetailPage';
import BookmarksPage from './pages/news/BookmarksPage';
import SubscriptionPage from './pages/subscription/SubscriptionPage';
import SubscriptionCreatePage from './pages/subscription/SubscriptionCreatePage';
import SubscriptionEditPage from './pages/subscription/SubscriptionEditPage';
import ProfilePage from './pages/profile/ProfilePage';
import SettingsPage from './pages/settings/SettingsPage';
import NotFoundPage from './pages/error/NotFoundPage';

// 路由守卫组件
import ProtectedRoute from './components/common/ProtectedRoute';
import PublicRoute from './components/common/PublicRoute';

// 全局组件
import LoadingSpinner from './components/common/LoadingSpinner';
import ErrorBoundary from './components/common/ErrorBoundary';

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, loading: authLoading, token } = useAppSelector(state => state.auth);
  const { theme: uiTheme, loading: uiLoading } = useAppSelector(state => state.ui);

  // 初始化应用
  useEffect(() => {
    // 初始化主题
    dispatch(initializeTheme());

    // 检测移动设备
    const checkMobile = () => {
      const isMobile = window.innerWidth <= 768;
      dispatch(setIsMobile(isMobile));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // 如果有token，尝试获取用户信息
    if (token && !isAuthenticated) {
      dispatch(getCurrentUser());
    }

    // 初始化性能监控
    performanceMonitor.startApiTiming('app_init');

    // 初始化预加载
    initializePreloading();

    // 性能监控：记录应用初始化完成
    setTimeout(() => {
      performanceMonitor.endApiTiming('app_init');

      // 检查性能阈值
      const { passed, issues } = performanceMonitor.checkPerformanceThresholds();
      if (!passed) {
        // 性能问题检测 - 保留用于生产环境监控
        if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
          console.warn('Performance issues detected:', issues);
        }
      }
    }, 100);

    return () => {
      window.removeEventListener('resize', checkMobile);
      performanceMonitor.cleanup();
    };
  }, [dispatch, token, isAuthenticated]);

  // Ant Design主题配置
  const antdTheme = {
    algorithm: uiTheme.darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: uiTheme.primaryColor,
      borderRadius: uiTheme.borderRadius,
      wireframe: false,
    },
    components: {
      Layout: {
        headerBg: uiTheme.darkMode ? '#141414' : '#001529',
        siderBg: uiTheme.darkMode ? '#141414' : '#001529',
        bodyBg: uiTheme.darkMode ? '#000000' : '#f5f5f5',
      },
      Menu: {
        darkItemBg: uiTheme.darkMode ? '#141414' : '#001529',
        darkSubMenuItemBg: uiTheme.darkMode ? '#1f1f1f' : '#000c17',
        darkItemSelectedBg: uiTheme.primaryColor,
      },
    },
  };

  // 显示加载状态
  if (authLoading || uiLoading) {
    return <LoadingSpinner size="large" tip="应用初始化中..." />;
  }

  return (
    <ErrorBoundary>
      <ConfigProvider theme={antdTheme}>
        <AntdApp>
          <Routes>
            {/* 公开路由 */}
            <Route path="/auth" element={<AuthLayout />}>
              <Route
                path="login"
                element={
                  <PublicRoute>
                    <LoginPage />
                  </PublicRoute>
                }
              />
              <Route
                path="register"
                element={
                  <PublicRoute>
                    <RegisterPage />
                  </PublicRoute>
                }
              />
              <Route
                path="forgot-password"
                element={
                  <PublicRoute>
                    <ForgotPasswordPage />
                  </PublicRoute>
                }
              />
              <Route
                path="callback/:provider"
                element={
                  <PublicRoute>
                    <OAuthCallbackPage />
                  </PublicRoute>
                }
              />
            </Route>

            {/* 受保护的路由 */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              {/* 默认重定向到仪表板 */}
              <Route index element={<Navigate to="/dashboard" replace />} />
              
              {/* 仪表板 */}
              <Route path="dashboard" element={<DashboardPage />} />
              
              {/* 新闻相关 */}
              <Route path="news" element={<NewsPage />} />
              <Route path="news/:id" element={<NewsDetailPage />} />
              <Route path="bookmarks" element={<BookmarksPage />} />
              
              {/* 订阅管理 */}
              <Route path="subscriptions" element={<SubscriptionPage />} />
              <Route path="subscriptions/create" element={<SubscriptionCreatePage />} />
              <Route path="subscriptions/:id/edit" element={<SubscriptionEditPage />} />
              
              {/* 用户相关 */}
              <Route path="profile" element={<ProfilePage />} />
              <Route path="settings" element={<SettingsPage />} />
            </Route>

            {/* 404页面 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;
