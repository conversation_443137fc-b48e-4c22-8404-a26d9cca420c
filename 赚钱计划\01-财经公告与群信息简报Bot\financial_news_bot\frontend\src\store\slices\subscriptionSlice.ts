import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Subscription, SubscriptionRequest } from '../../types';

interface SubscriptionState {
  subscriptions: Subscription[];
  currentSubscription: Subscription | null;
  loading: boolean;
  error: string | null;
}

const initialState: SubscriptionState = {
  subscriptions: [],
  currentSubscription: null,
  loading: false,
  error: null,
};

// 异步thunks
export const fetchSubscriptions = createAsyncThunk(
  'subscription/fetchSubscriptions',
  async (_, { rejectWithValue }) => {
    try {
      // 这里调用API获取订阅列表
      // const response = await subscriptionService.getSubscriptions();
      // return response;
      
      // 暂时返回模拟数据
      return [] as Subscription[];
    } catch (error: unknown) {
      return rejectWithValue((error as { response?: { data?: { message?: string } } }).response?.data?.message || '获取订阅列表失败');
    }
  }
);

export const createSubscription = createAsyncThunk(
  'subscription/createSubscription',
  async (data: SubscriptionRequest, { rejectWithValue }) => {
    try {
      // 这里调用API创建订阅
      // const response = await subscriptionService.createSubscription(data);
      // return response;
      
      // 暂时返回模拟数据
      return {} as Subscription;
    } catch (error: unknown) {
      return rejectWithValue((error as { response?: { data?: { message?: string } } }).response?.data?.message || '创建订阅失败');
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentSubscription: (state, action: PayloadAction<Subscription | null>) => {
      state.currentSubscription = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch subscriptions
    builder
      .addCase(fetchSubscriptions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptions.fulfilled, (state, action) => {
        state.loading = false;
        state.subscriptions = action.payload;
        state.error = null;
      })
      .addCase(fetchSubscriptions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Create subscription
    builder
      .addCase(createSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscriptions.push(action.payload);
        state.error = null;
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentSubscription } = subscriptionSlice.actions;
export default subscriptionSlice.reducer;
