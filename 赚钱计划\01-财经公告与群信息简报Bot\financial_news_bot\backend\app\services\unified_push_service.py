"""
统一推送服务
整合所有推送功能，提供简化的统一接口
基于策略模式设计，支持多种推送模式和渠道
"""
import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum
import json

from app.models.user import User
from app.models.news import News
from app.services.cache_service import cache_service
from app.config import settings

# 推送提供商将在运行时动态导入，避免循环导入

logger = logging.getLogger(__name__)

class PushMode(Enum):
    """推送模式枚举"""
    BASIC = "basic"           # 基础推送
    ENHANCED = "enhanced"     # 增强推送（带重试和监控）
    LAYERED = "layered"       # 分层推送（按优先级）
    ANALYTICS = "analytics"   # 带分析的推送
    BATCH = "batch"          # 批量推送

class PushChannel(Enum):
    """推送渠道枚举"""
    WECHAT_WORK = "wechat_work"
    FEISHU = "feishu"
    EMAIL = "email"
    WECHAT_GROUP = "wechat_group"

class MessageType(Enum):
    """消息类型枚举"""
    TEXT = "text"
    MARKDOWN = "markdown"
    CARD = "card"
    HTML = "html"

class PushMessage:
    """推送消息数据类"""
    def __init__(self, content: str, title: str = "", message_type: MessageType = MessageType.TEXT, **kwargs):
        self.content = content
        self.title = title
        self.message_type = message_type
        self.extra_data = kwargs

class PushResult:
    """推送结果数据类"""
    def __init__(self, success: bool, message: str = "", error_code: str = None, **kwargs):
        self.success = success
        self.message = message
        self.error_code = error_code
        self.extra_data = kwargs

class BasePushProvider(ABC):
    """推送提供商基类"""

    @abstractmethod
    async def send_message(self, message: PushMessage, recipients: List[str]) -> PushResult:
        """发送消息的抽象方法"""
        pass

    @abstractmethod
    def get_channel(self) -> PushChannel:
        """获取推送渠道"""
        pass

class PushStatus(Enum):
    """推送状态枚举"""
    PENDING = "pending"
    SENDING = "sending"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"

class PushMessage:
    """统一推送消息数据结构"""
    
    def __init__(self, 
                 title: str,
                 content: str,
                 message_type: MessageType = MessageType.TEXT,
                 channels: List[PushChannel] = None,
                 targets: List[str] = None,
                 extra_data: Dict = None):
        self.title = title
        self.content = content
        self.message_type = message_type
        self.channels = channels or [PushChannel.WECHAT_WORK]
        self.targets = targets or []
        self.extra_data = extra_data or {}
        self.created_at = datetime.now()

class PushResult:
    """推送结果数据结构"""
    
    def __init__(self, 
                 success: bool = False,
                 message: str = "",
                 channel: PushChannel = None,
                 target: str = "",
                 response_data: Dict = None):
        self.success = success
        self.message = message
        self.channel = channel
        self.target = target
        self.response_data = response_data or {}
        self.timestamp = datetime.now()

class PushStrategy(ABC):
    """推送策略抽象基类"""
    
    @abstractmethod
    async def execute(self, message: PushMessage, providers: Dict[PushChannel, Any]) -> List[PushResult]:
        """执行推送策略"""
        pass

class BasicPushStrategy(PushStrategy):
    """基础推送策略 - 简单直接的推送"""
    
    async def execute(self, message: PushMessage, providers: Dict[PushChannel, Any]) -> List[PushResult]:
        """执行基础推送"""
        results = []
        
        for channel in message.channels:
            if channel not in providers:
                results.append(PushResult(
                    success=False,
                    message=f"推送渠道 {channel.value} 未配置",
                    channel=channel
                ))
                continue
            
            provider = providers[channel]
            
            try:
                # 执行推送
                result = await provider.send_message(
                    title=message.title,
                    content=message.content,
                    message_type=message.message_type.value,
                    targets=message.targets
                )
                
                results.append(PushResult(
                    success=result.get('success', False),
                    message=result.get('message', ''),
                    channel=channel,
                    response_data=result
                ))
                
            except Exception as e:
                logger.error(f"基础推送失败 - 渠道: {channel.value}, 错误: {str(e)}")
                results.append(PushResult(
                    success=False,
                    message=f"推送失败: {str(e)}",
                    channel=channel
                ))
        
        return results

class EnhancedPushStrategy(PushStrategy):
    """增强推送策略 - 带重试和监控"""
    
    def __init__(self, max_retries: int = 3, retry_delay: int = 5):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    async def execute(self, message: PushMessage, providers: Dict[PushChannel, Any]) -> List[PushResult]:
        """执行增强推送"""
        results = []
        
        for channel in message.channels:
            if channel not in providers:
                results.append(PushResult(
                    success=False,
                    message=f"推送渠道 {channel.value} 未配置",
                    channel=channel
                ))
                continue
            
            provider = providers[channel]
            
            # 带重试的推送
            for attempt in range(self.max_retries + 1):
                try:
                    result = await provider.send_message(
                        title=message.title,
                        content=message.content,
                        message_type=message.message_type.value,
                        targets=message.targets
                    )
                    
                    if result.get('success', False):
                        results.append(PushResult(
                            success=True,
                            message=f"推送成功 (尝试 {attempt + 1})",
                            channel=channel,
                            response_data=result
                        ))
                        break
                    else:
                        if attempt < self.max_retries:
                            logger.warning(f"推送失败，{self.retry_delay}秒后重试 - 渠道: {channel.value}")
                            await asyncio.sleep(self.retry_delay)
                        else:
                            results.append(PushResult(
                                success=False,
                                message=f"推送失败，已重试 {self.max_retries} 次",
                                channel=channel,
                                response_data=result
                            ))
                
                except Exception as e:
                    if attempt < self.max_retries:
                        logger.warning(f"推送异常，{self.retry_delay}秒后重试 - 渠道: {channel.value}, 错误: {str(e)}")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        logger.error(f"增强推送失败 - 渠道: {channel.value}, 错误: {str(e)}")
                        results.append(PushResult(
                            success=False,
                            message=f"推送异常: {str(e)}",
                            channel=channel
                        ))
        
        return results

class LayeredPushStrategy(PushStrategy):
    """分层推送策略 - 按优先级推送"""
    
    def __init__(self):
        # 定义渠道优先级（数字越小优先级越高）
        self.channel_priority = {
            PushChannel.WECHAT_WORK: 1,
            PushChannel.FEISHU: 2,
            PushChannel.WECHAT_GROUP: 3,
            PushChannel.EMAIL: 4
        }
    
    async def execute(self, message: PushMessage, providers: Dict[PushChannel, Any]) -> List[PushResult]:
        """执行分层推送"""
        results = []
        
        # 按优先级排序渠道
        sorted_channels = sorted(
            message.channels,
            key=lambda x: self.channel_priority.get(x, 999)
        )
        
        for channel in sorted_channels:
            if channel not in providers:
                results.append(PushResult(
                    success=False,
                    message=f"推送渠道 {channel.value} 未配置",
                    channel=channel
                ))
                continue
            
            provider = providers[channel]
            
            try:
                result = await provider.send_message(
                    title=message.title,
                    content=message.content,
                    message_type=message.message_type.value,
                    targets=message.targets
                )
                
                push_result = PushResult(
                    success=result.get('success', False),
                    message=result.get('message', ''),
                    channel=channel,
                    response_data=result
                )
                results.append(push_result)
                
                # 如果高优先级渠道推送成功，可以选择跳过低优先级渠道
                if push_result.success and message.extra_data.get('stop_on_success', False):
                    logger.info(f"高优先级渠道 {channel.value} 推送成功，跳过其他渠道")
                    break
                
            except Exception as e:
                logger.error(f"分层推送失败 - 渠道: {channel.value}, 错误: {str(e)}")
                results.append(PushResult(
                    success=False,
                    message=f"推送失败: {str(e)}",
                    channel=channel
                ))
        
        return results

class AnalyticsPushStrategy(PushStrategy):
    """带分析的推送策略 - 记录推送统计"""
    
    async def execute(self, message: PushMessage, providers: Dict[PushChannel, Any]) -> List[PushResult]:
        """执行带分析的推送"""
        start_time = datetime.now()
        results = []
        
        # 记录推送开始
        analytics_data = {
            'start_time': start_time.isoformat(),
            'channels': [c.value for c in message.channels],
            'message_type': message.message_type.value,
            'target_count': len(message.targets)
        }
        
        # 执行基础推送
        basic_strategy = BasicPushStrategy()
        results = await basic_strategy.execute(message, providers)
        
        # 记录推送结果统计
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count
        
        analytics_data.update({
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'total_attempts': len(results),
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': success_count / len(results) if results else 0
        })
        
        # 缓存分析数据
        cache_key = f"push_analytics:{start_time.strftime('%Y%m%d_%H%M%S')}"
        await cache_service.set(cache_key, analytics_data, expire=86400)  # 缓存24小时
        
        logger.info(f"推送分析完成 - 成功: {success_count}, 失败: {failed_count}, 耗时: {duration:.2f}秒")
        
        return results

class UnifiedPushService:
    """统一推送服务 - 整合所有推送功能的主服务"""
    
    def __init__(self):
        self.providers = self._init_providers()
        self.strategies = self._init_strategies()
        logger.info("统一推送服务初始化完成")
    
    def _init_providers(self) -> Dict[PushChannel, Any]:
        """初始化推送提供商"""
        providers = {}

        try:
            # 动态导入推送提供商，避免循环导入
            from app.services.push_providers.email_provider import EmailProvider
            from app.services.push_providers.feishu_provider import FeishuProvider
            from app.services.push_providers.wechat_group_provider import WeChatGroupProvider
            from app.services.push_providers.wechat_work_provider import WeChatWorkProvider

            # 初始化各个推送提供商
            providers[PushChannel.WECHAT_WORK] = WeChatWorkProvider()
            providers[PushChannel.FEISHU] = FeishuProvider()
            providers[PushChannel.EMAIL] = EmailProvider()
            providers[PushChannel.WECHAT_GROUP] = WeChatGroupProvider()

            logger.info(f"推送提供商初始化完成，支持渠道: {list(providers.keys())}")
        except Exception as e:
            logger.error(f"推送提供商初始化失败: {str(e)}")

        return providers
    
    def _init_strategies(self) -> Dict[PushMode, PushStrategy]:
        """初始化推送策略"""
        return {
            PushMode.BASIC: BasicPushStrategy(),
            PushMode.ENHANCED: EnhancedPushStrategy(max_retries=3, retry_delay=5),
            PushMode.LAYERED: LayeredPushStrategy(),
            PushMode.ANALYTICS: AnalyticsPushStrategy(),
            PushMode.BATCH: BasicPushStrategy()  # 批量推送使用基础策略
        }
    
    async def send_message(self, 
                          title: str,
                          content: str,
                          mode: PushMode = PushMode.BASIC,
                          channels: List[PushChannel] = None,
                          targets: List[str] = None,
                          message_type: MessageType = MessageType.TEXT,
                          **kwargs) -> List[PushResult]:
        """
        统一推送接口
        
        Args:
            title: 消息标题
            content: 消息内容
            mode: 推送模式
            channels: 推送渠道列表
            targets: 目标用户/群组列表
            message_type: 消息类型
            **kwargs: 额外参数
        
        Returns:
            推送结果列表
        """
        # 创建推送消息
        message = PushMessage(
            title=title,
            content=content,
            message_type=message_type,
            channels=channels or [PushChannel.WECHAT_WORK],
            targets=targets or [],
            extra_data=kwargs
        )
        
        # 获取推送策略
        strategy = self.strategies.get(mode, self.strategies[PushMode.BASIC])
        
        # 执行推送
        try:
            results = await strategy.execute(message, self.providers)
            
            # 记录推送日志
            self._log_push_results(message, results)
            
            return results
            
        except Exception as e:
            logger.error(f"统一推送服务执行失败: {str(e)}")
            return [PushResult(
                success=False,
                message=f"推送服务异常: {str(e)}"
            )]
    
    def _log_push_results(self, message: PushMessage, results: List[PushResult]):
        """记录推送结果日志"""
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        logger.info(
            f"推送完成 - 标题: {message.title[:50]}..., "
            f"渠道: {[c.value for c in message.channels]}, "
            f"成功: {success_count}/{total_count}"
        )
        
        # 记录失败的推送
        for result in results:
            if not result.success:
                logger.warning(
                    f"推送失败 - 渠道: {result.channel.value if result.channel else 'unknown'}, "
                    f"错误: {result.message}"
                )

# 全局统一推送服务实例
unified_push_service = UnifiedPushService()
