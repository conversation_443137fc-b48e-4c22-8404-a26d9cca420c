import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Space,
  Tag,
  Avatar,
  Divider,
  Row,
  Col,
  Affix,
  BackTop,
  message,
  Spin,
  List,
  Form,
  Input,
} from 'antd';
import {
  ArrowLeftOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  HeartOutlined,
  HeartFilled,
  ShareAltOutlined,
  BookOutlined,
  UserOutlined,
  LikeOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface NewsDetail {
  id: number;
  title: string;
  summary: string;
  content: string;
  source: string;
  author: string;
  published_at: string;
  updated_at?: string;
  importance: 'high' | 'medium' | 'low';
  category: string;
  tags: string[];
  views: number;
  likes: number;
  is_liked: boolean;
  is_bookmarked: boolean;
  image_url?: string;
  source_url?: string;
  related_news: Array<{
    id: number;
    title: string;
    published_at: string;
  }>;
}

interface CommentItem {
  id: number;
  user_name: string;
  user_avatar?: string;
  content: string;
  created_at: string;
  likes: number;
  is_liked: boolean;
  replies?: CommentItem[];
}

const NewsDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [loading, setLoading] = useState(true);
  const [newsDetail, setNewsDetail] = useState<NewsDetail | null>(null);
  const [comments, setComments] = useState<CommentItem[]>([]);
  const [commentForm] = Form.useForm();
  const [submittingComment, setSubmittingComment] = useState(false);

  useEffect(() => {
    if (id) {
      loadNewsDetail(parseInt(id));
      loadComments(parseInt(id));
    }
  }, [id]);

  useEffect(() => {
    if (newsDetail) {
      dispatch(setPageTitle(newsDetail.title));
    }
  }, [newsDetail, dispatch]);

  // 真实新闻详情数据状态（移除所有模拟数据）

  const loadNewsDetail = async (newsId: number) => {
    setLoading(true);
    try {
      // 真实API调用
      const response = await fetch(`/api/v1/news/${newsId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setNewsDetail(data);
      } else {
        message.error('获取新闻详情失败');
      }
    } catch (error) {
      // 新闻详情加载错误日志 - 用于调试和监控
      console.error('加载新闻详情失败:', error);
      message.error('加载新闻详情失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const loadComments = async (newsId: number) => {
    try {
      // 真实API调用
      const response = await fetch(`/api/v1/news/${newsId}/comments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setComments(data.items || []);
      } else {
        // 评论获取失败日志 - 用于调试和监控
        console.error('获取评论失败');
        setComments([]);
      }
    } catch (error) {
      // 评论加载错误日志 - 用于调试和监控
      console.error('加载评论失败:', error);
      setComments([]);
    }
  };

  const handleLike = () => {
    if (!newsDetail) return;

    setNewsDetail(prev => prev ? {
      ...prev,
      is_liked: !prev.is_liked,
      likes: prev.is_liked ? prev.likes - 1 : prev.likes + 1
    } : null);
  };

  const handleBookmark = () => {
    if (!newsDetail) return;

    setNewsDetail(prev => prev ? {
      ...prev,
      is_bookmarked: !prev.is_bookmarked
    } : null);
    message.success(newsDetail.is_bookmarked ? '取消收藏成功' : '收藏成功');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: newsDetail?.title,
        text: newsDetail?.summary,
        url: window.location.href,
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      message.success('链接已复制到剪贴板');
    }
  };

  const handleCommentSubmit = async (values: { content: string }) => {
    if (!newsDetail) return;

    setSubmittingComment(true);
    try {
      // 真实API调用
      const response = await fetch(`/api/v1/news/${newsDetail.id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({ content: values.content }),
      });

      if (response.ok) {
        const newComment = await response.json();
        setComments(prev => [newComment, ...prev]);
        commentForm.resetFields();
        message.success('评论发布成功');
      } else {
        const error = await response.text();
        message.error(`评论发布失败: ${error}`);
      }
    } catch (error) {
      // 评论发布错误日志 - 用于调试和监控
      console.error('发布评论失败:', error);
      message.error('评论发布失败，请检查网络连接');
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleCommentLike = (commentId: number) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? {
            ...comment,
            is_liked: !comment.is_liked,
            likes: comment.is_liked ? comment.likes - 1 : comment.likes + 1
          }
        : comment
    ));
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!newsDetail) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Text>新闻不存在或已被删除</Text>
          <br />
          <Button type="primary" onClick={() => navigate('/news')} style={{ marginTop: '16px' }}>
            返回新闻列表
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Row gutter={24}>
        {/* 主内容区 */}
        <Col xs={24} lg={18}>
          <Card>
            {/* 返回按钮 */}
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/news')}
              style={{ marginBottom: '16px' }}
            >
              返回新闻列表
            </Button>

            {/* 新闻头部信息 */}
            <div style={{ marginBottom: '24px' }}>
              <Space wrap style={{ marginBottom: '16px' }}>
                <Tag color="blue">{newsDetail.category}</Tag>
                <Tag color={newsDetail.importance === 'high' ? 'red' : 'orange'}>
                  {newsDetail.importance === 'high' ? '重要' : '一般'}
                </Tag>
                {newsDetail.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>

              <Title level={1} style={{ marginBottom: '16px' }}>
                {newsDetail.title}
              </Title>

              <Paragraph style={{ fontSize: '16px', color: '#666', marginBottom: '16px' }}>
                {newsDetail.summary}
              </Paragraph>

              <Row justify="space-between" align="middle">
                <Col>
                  <Space>
                    <Avatar icon={<UserOutlined />} />
                    <div>
                      <Text strong>{newsDetail.author}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {newsDetail.source}
                      </Text>
                    </div>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <Text type="secondary">
                      <ClockCircleOutlined /> {dayjs(newsDetail.published_at).format('YYYY-MM-DD HH:mm')}
                    </Text>
                    <Text type="secondary">
                      <EyeOutlined /> {newsDetail.views}
                    </Text>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            {/* 新闻图片 */}
            {newsDetail.image_url && (
              <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                <img
                  src={newsDetail.image_url}
                  alt={newsDetail.title}
                  style={{ maxWidth: '100%', borderRadius: '8px' }}
                />
              </div>
            )}

            {/* 新闻内容 */}
            <div
              style={{
                fontSize: '16px',
                lineHeight: '1.8',
                marginBottom: '32px'
              }}
              dangerouslySetInnerHTML={{ __html: newsDetail.content }}
            />

            {/* 原文链接 */}
            {newsDetail.source_url && (
              <div style={{ marginBottom: '24px' }}>
                <Text type="secondary">
                  原文链接：
                  <a href={newsDetail.source_url} target="_blank" rel="noopener noreferrer">
                    {newsDetail.source_url}
                  </a>
                </Text>
              </div>
            )}

            <Divider />

            {/* 操作按钮 */}
            <Row justify="space-between" align="middle" style={{ marginBottom: '32px' }}>
              <Col>
                <Space size="large">
                  <Button
                    type={newsDetail.is_liked ? 'primary' : 'default'}
                    icon={newsDetail.is_liked ? <HeartFilled /> : <HeartOutlined />}
                    onClick={handleLike}
                  >
                    {newsDetail.likes} 点赞
                  </Button>
                  <Button
                    type={newsDetail.is_bookmarked ? 'primary' : 'default'}
                    icon={<BookOutlined />}
                    onClick={handleBookmark}
                  >
                    {newsDetail.is_bookmarked ? '已收藏' : '收藏'}
                  </Button>
                  <Button icon={<ShareAltOutlined />} onClick={handleShare}>
                    分享
                  </Button>
                </Space>
              </Col>
            </Row>

            {/* 评论区 */}
            <div>
              <Title level={3}>评论 ({comments.length})</Title>

              {/* 发表评论 */}
              <Form
                form={commentForm}
                onFinish={handleCommentSubmit}
                style={{ marginBottom: '24px' }}
              >
                <Form.Item
                  name="content"
                  rules={[
                    { required: true, message: '请输入评论内容' },
                    { min: 5, message: '评论内容至少5个字符' },
                  ]}
                >
                  <TextArea
                    rows={4}
                    placeholder="写下你的评论..."
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={submittingComment}>
                    发表评论
                  </Button>
                </Form.Item>
              </Form>

              {/* 评论列表 */}
              <List
                dataSource={comments}
                renderItem={(comment) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<UserOutlined />} />}
                      title={
                        <Space>
                          <span>{comment.user_name}</span>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {dayjs(comment.created_at).format('MM-DD HH:mm')}
                          </Text>
                        </Space>
                      }
                      description={comment.content}
                    />
                    <div>
                      <Button
                        type="text"
                        size="small"
                        icon={comment.is_liked ? <LikeOutlined style={{ color: '#1890ff' }} /> : <LikeOutlined />}
                        onClick={() => handleCommentLike(comment.id)}
                      >
                        {comment.likes}
                      </Button>
                    </div>
                  </List.Item>
                )}
              />
            </div>
          </Card>
        </Col>

        {/* 侧边栏 */}
        <Col xs={24} lg={6}>
          <Affix offsetTop={24}>
            <Card title="相关新闻" size="small">
              <List
                size="small"
                dataSource={newsDetail.related_news}
                renderItem={(item) => (
                  <List.Item>
                    <div>
                      <a href={`/news/${item.id}`} style={{ fontSize: '14px' }}>
                        {item.title}
                      </a>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {dayjs(item.published_at).format('MM-DD HH:mm')}
                      </Text>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Affix>
        </Col>
      </Row>

      <BackTop />
    </div>
  );
};

export default NewsDetailPage;
