# 财经新闻Bot项目 .gitignore 文件
# 包含Python、Node.js、<PERSON><PERSON>、IDE等相关忽略规则

# ==========================================
# Python相关
# ==========================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ==========================================
# Node.js相关
# ==========================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ==========================================
# React相关
# ==========================================
# production build
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# ==========================================
# Docker相关
# ==========================================
# Docker volumes
docker-volumes/
.docker/

# ==========================================
# 数据库相关
# ==========================================
# MySQL数据文件
*.sql
*.db
*.sqlite
*.sqlite3

# 数据库备份文件
*.dump
*.backup

# ==========================================
# IDE和编辑器相关
# ==========================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.swn

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==========================================
# 操作系统相关
# ==========================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.target
.metadata
.classpath
.project

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 项目特定文件
# ==========================================
# 日志文件
logs/
*.log

# 配置文件（包含敏感信息）
.env
.env.local
.env.production
config/local.py
config/production.py

# 上传文件
uploads/
media/
static/media/

# 缓存文件
.cache/
cache/

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup
*.old

# 测试输出
test-results/
coverage/
.coverage

# 部署相关
deploy/
deployment/
.deploy/

# 证书和密钥文件
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# 数据文件
data/
*.csv
*.json
*.xml
!package.json
!package-lock.json
!tsconfig.json

# AI模型文件
*.model
*.pkl
*.joblib

# 监控和分析文件
*.prof
*.trace
