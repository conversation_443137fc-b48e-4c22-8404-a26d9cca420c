import { useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  description: string;
  action: () => void;
  preventDefault?: boolean;
  disabled?: boolean;
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  showHelp?: boolean;
}

const useKeyboardShortcuts = (
  shortcuts: ShortcutConfig[],
  options: UseKeyboardShortcutsOptions = {}
) => {
  const { enabled = true, showHelp = true } = options;
  const shortcutsRef = useRef<ShortcutConfig[]>(shortcuts);
  const helpModalRef = useRef<boolean>(false);

  // 更新快捷键配置
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  // 检查快捷键是否匹配
  const isShortcutMatch = useCallback((event: KeyboardEvent, shortcut: ShortcutConfig) => {
    const key = event.key.toLowerCase();
    const shortcutKey = shortcut.key.toLowerCase();

    // 检查主键
    if (key !== shortcutKey) return false;

    // 检查修饰键
    if (!!shortcut.ctrl !== event.ctrlKey) return false;
    if (!!shortcut.alt !== event.altKey) return false;
    if (!!shortcut.shift !== event.shiftKey) return false;
    if (!!shortcut.meta !== event.metaKey) return false;

    return true;
  }, []);

  // 处理键盘事件
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // 忽略在输入框中的快捷键
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      // 只允许特定的快捷键在输入框中工作
      const allowedInInput = ['escape', 'f1'];
      if (!allowedInInput.includes(event.key.toLowerCase())) {
        return;
      }
    }

    // 查找匹配的快捷键
    const matchedShortcut = shortcutsRef.current.find(shortcut => 
      !shortcut.disabled && isShortcutMatch(event, shortcut)
    );

    if (matchedShortcut) {
      if (matchedShortcut.preventDefault !== false) {
        event.preventDefault();
        event.stopPropagation();
      }

      try {
        matchedShortcut.action();
      } catch (error) {
        // 快捷键执行错误日志 - 用于调试快捷键功能问题
        console.error('快捷键执行错误:', error);
        message.error('快捷键执行失败');
      }
    }
  }, [enabled, isShortcutMatch]);

  // 显示快捷键帮助
  const showShortcutHelp = useCallback(() => {
    if (helpModalRef.current) return;
    
    helpModalRef.current = true;
    
    const helpContent = shortcutsRef.current
      .filter(shortcut => !shortcut.disabled)
      .map(shortcut => {
        const keys = [];
        if (shortcut.ctrl) keys.push('Ctrl');
        if (shortcut.alt) keys.push('Alt');
        if (shortcut.shift) keys.push('Shift');
        if (shortcut.meta) keys.push('Cmd');
        keys.push(shortcut.key.toUpperCase());
        
        return `${keys.join(' + ')}: ${shortcut.description}`;
      })
      .join('\n');

    // 创建帮助模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      max-width: 500px;
      max-height: 70vh;
      overflow-y: auto;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;

    content.innerHTML = `
      <h3 style="margin: 0 0 16px 0; color: #262626;">快捷键帮助</h3>
      <div style="white-space: pre-line; line-height: 1.6; color: #595959; font-family: monospace;">
        ${helpContent}
      </div>
      <div style="margin-top: 16px; text-align: right;">
        <button id="close-help" style="
          background: #1890ff;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        ">关闭</button>
      </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    const closeHelp = () => {
      document.body.removeChild(modal);
      helpModalRef.current = false;
    };

    // 点击关闭按钮
    content.querySelector('#close-help')?.addEventListener('click', closeHelp);
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeHelp();
    });

    // ESC键关闭
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeHelp();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }, []);

  // 注册事件监听器
  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enabled, handleKeyDown]);

  // 添加帮助快捷键
  useEffect(() => {
    if (showHelp && enabled) {
      const helpShortcut: ShortcutConfig = {
        key: 'F1',
        description: '显示快捷键帮助',
        action: showShortcutHelp,
      };

      shortcutsRef.current = [...shortcuts, helpShortcut];
    }
  }, [shortcuts, showHelp, enabled, showShortcutHelp]);

  return {
    showHelp: showShortcutHelp,
  };
};

// 预定义的常用快捷键
export const commonShortcuts = {
  // 导航快捷键
  goToHome: (action: () => void): ShortcutConfig => ({
    key: 'h',
    ctrl: true,
    description: '返回首页',
    action,
  }),

  goToNews: (action: () => void): ShortcutConfig => ({
    key: 'n',
    ctrl: true,
    description: '新闻中心',
    action,
  }),

  goToSubscriptions: (action: () => void): ShortcutConfig => ({
    key: 's',
    ctrl: true,
    description: '订阅管理',
    action,
  }),

  goToProfile: (action: () => void): ShortcutConfig => ({
    key: 'p',
    ctrl: true,
    description: '个人资料',
    action,
  }),

  // 操作快捷键
  newSubscription: (action: () => void): ShortcutConfig => ({
    key: 'n',
    ctrl: true,
    shift: true,
    description: '新建订阅',
    action,
  }),

  search: (action: () => void): ShortcutConfig => ({
    key: 'k',
    ctrl: true,
    description: '全局搜索',
    action,
  }),

  save: (action: () => void): ShortcutConfig => ({
    key: 's',
    ctrl: true,
    description: '保存',
    action,
  }),

  refresh: (action: () => void): ShortcutConfig => ({
    key: 'r',
    ctrl: true,
    description: '刷新',
    action,
  }),

  // 编辑快捷键
  undo: (action: () => void): ShortcutConfig => ({
    key: 'z',
    ctrl: true,
    description: '撤销',
    action,
  }),

  redo: (action: () => void): ShortcutConfig => ({
    key: 'y',
    ctrl: true,
    description: '重做',
    action,
  }),

  selectAll: (action: () => void): ShortcutConfig => ({
    key: 'a',
    ctrl: true,
    description: '全选',
    action,
  }),

  // 其他快捷键
  toggleTheme: (action: () => void): ShortcutConfig => ({
    key: 't',
    ctrl: true,
    shift: true,
    description: '切换主题',
    action,
  }),

  logout: (action: () => void): ShortcutConfig => ({
    key: 'l',
    ctrl: true,
    shift: true,
    description: '退出登录',
    action,
  }),
};

export default useKeyboardShortcuts;
