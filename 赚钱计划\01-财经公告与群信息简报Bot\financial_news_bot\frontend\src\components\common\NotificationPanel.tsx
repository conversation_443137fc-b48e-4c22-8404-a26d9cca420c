import React from 'react';
import { Drawer, <PERSON>, Typography, Button, Empty, Badge, Space } from 'antd';
import { useAppSelector, useAppDispatch } from '../../store';
import { markNotificationAsRead, markAllNotificationsAsRead, removeNotification } from '../../store/slices/uiSlice';

const { Text } = Typography;

interface NotificationPanelProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ visible, onClose }) => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector(state => state.ui);

  const unreadCount = notifications.filter(n => !n.read).length;

  const handleMarkAsRead = (id: string) => {
    dispatch(markNotificationAsRead(id));
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllNotificationsAsRead());
  };

  const handleRemove = (id: string) => {
    dispatch(removeNotification(id));
  };

  return (
    <Drawer
      title={
        <Space>
          <span>通知</span>
          {unreadCount > 0 && <Badge count={unreadCount} />}
        </Space>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
      extra={
        unreadCount > 0 && (
          <Button type="link" onClick={handleMarkAllAsRead}>
            全部已读
          </Button>
        )
      }
    >
      {notifications.length === 0 ? (
        <Empty description="暂无通知" />
      ) : (
        <List
          dataSource={notifications}
          renderItem={(item) => (
            <List.Item
              style={{
                backgroundColor: item.read ? 'transparent' : '#f6ffed',
                padding: '12px',
                borderRadius: '6px',
                marginBottom: '8px',
              }}
              actions={[
                !item.read && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => handleMarkAsRead(item.id)}
                  >
                    标记已读
                  </Button>
                ),
                <Button
                  type="link"
                  size="small"
                  danger
                  onClick={() => handleRemove(item.id)}
                >
                  删除
                </Button>,
              ].filter(Boolean)}
            >
              <List.Item.Meta
                title={
                  <Text strong={!item.read}>
                    {item.title}
                  </Text>
                }
                description={
                  <>
                    <Text>{item.message}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {new Date(item.timestamp).toLocaleString()}
                    </Text>
                  </>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Drawer>
  );
};

export default NotificationPanel;
