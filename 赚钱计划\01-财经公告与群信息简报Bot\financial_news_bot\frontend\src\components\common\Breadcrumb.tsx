import React from 'react';
import { Breadcrumb as AntdBreadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { HomeOutlined } from '@ant-design/icons';

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  
  // 路径映射
  const pathMap: Record<string, string> = {
    '/dashboard': '仪表板',
    '/news': '新闻中心',
    '/subscriptions': '订阅管理',
    '/subscriptions/create': '创建订阅',
    '/profile': '个人资料',
    '/settings': '系统设置',
  };

  // 生成面包屑项
  const generateBreadcrumbs = () => {
    const pathSnippets = location.pathname.split('/').filter(i => i);
    
    const breadcrumbItems = [
      {
        title: (
          <Link to="/dashboard">
            <HomeOutlined />
          </Link>
        ),
      },
    ];

    pathSnippets.forEach((snippet, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
      const title = pathMap[url] || snippet;
      
      if (index === pathSnippets.length - 1) {
        // 最后一项不需要链接
        breadcrumbItems.push({ title: <span>{title}</span> });
      } else {
        breadcrumbItems.push({
          title: <Link to={url}>{title}</Link>,
        });
      }
    });

    return breadcrumbItems;
  };

  // 如果在首页，不显示面包屑
  if (location.pathname === '/dashboard') {
    return null;
  }

  return (
    <AntdBreadcrumb
      items={generateBreadcrumbs()}
      style={{ margin: '0 16px' }}
    />
  );
};

export default Breadcrumb;
