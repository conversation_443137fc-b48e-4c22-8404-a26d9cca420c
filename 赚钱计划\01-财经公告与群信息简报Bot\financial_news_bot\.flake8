[flake8]
max-line-length = 120
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    node_modules,
    .tox,
    .coverage,
    .coverage.*,
    .cache,
    nosetests.xml,
    coverage.xml,
    *.cover,
    *.py,cover,
    .hypothesis,
    .pytest_cache,
    migrations,
    alembic/versions,
    backend/alembic/versions

ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by max-line-length)
    E501

per-file-ignores =
    __init__.py:F401
    */migrations/*:E501,F401
    */alembic/versions/*:E501,F401

max-complexity = 15