#!/usr/bin/env python3
"""
环境变量验证脚本
验证.env文件中的所有必需配置项是否正确设置
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_env_file(env_path: str) -> Dict[str, str]:
    """加载.env文件"""
    env_vars = {}
    if not os.path.exists(env_path):
        return env_vars
    
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    return env_vars

def validate_required_vars() -> List[Tuple[str, str, bool]]:
    """验证必需的环境变量"""
    required_vars = [
        # 数据库配置
        ("DATABASE_URL", "MySQL数据库连接URL"),
        ("MYSQL_ROOT_PASSWORD", "MySQL root密码"),
        ("MYSQL_DATABASE", "MySQL数据库名"),
        ("MYSQL_USER", "MySQL用户名"),
        
        # JWT和安全配置
        ("SECRET_KEY", "JWT密钥"),
        ("JWT_SECRET_KEY", "JWT密钥"),
        
        # AI服务配置
        ("GLM_API_KEY", "GLM AI API密钥"),
        
        # Redis配置
        ("REDIS_URL", "Redis连接URL"),
    ]
    
    results = []
    for var_name, description in required_vars:
        value = os.getenv(var_name)
        is_valid = bool(value and value.strip())
        results.append((var_name, description, is_valid))
    
    return results

def validate_optional_vars() -> List[Tuple[str, str, bool]]:
    """验证可选的环境变量"""
    optional_vars = [
        ("DEBUG", "调试模式"),
        ("LOG_LEVEL", "日志级别"),
        ("TZ", "时区设置"),
        ("BACKEND_PORT", "后端端口"),
        ("NGINX_PORT", "Nginx端口"),
        ("WECHAT_GROUP_WEBHOOK", "微信群Webhook"),
        ("FEISHU_WEBHOOK", "飞书Webhook"),
        ("EMAIL_SMTP_HOST", "邮件SMTP主机"),
    ]
    
    results = []
    for var_name, description in optional_vars:
        value = os.getenv(var_name)
        is_set = bool(value and value.strip())
        results.append((var_name, description, is_set))
    
    return results

def validate_security_settings() -> List[Tuple[str, str, bool, str]]:
    """验证安全相关设置"""
    security_checks = []
    
    # 检查SECRET_KEY长度
    secret_key = os.getenv("SECRET_KEY", "")
    is_secure = len(secret_key) >= 32
    security_checks.append((
        "SECRET_KEY长度", 
        "JWT密钥长度应至少32字符", 
        is_secure,
        f"当前长度: {len(secret_key)}"
    ))
    
    # 检查DEBUG模式
    debug_mode = os.getenv("DEBUG", "false").lower()
    is_production_safe = debug_mode in ("false", "0", "no")
    security_checks.append((
        "DEBUG模式", 
        "生产环境应关闭DEBUG模式", 
        is_production_safe,
        f"当前值: {debug_mode}"
    ))
    
    # 检查数据库密码强度
    db_password = os.getenv("MYSQL_ROOT_PASSWORD", "")
    is_strong = len(db_password) >= 8 and any(c.isdigit() for c in db_password) and any(c.isalpha() for c in db_password)
    security_checks.append((
        "数据库密码强度", 
        "密码应至少8位且包含字母和数字", 
        is_strong,
        f"长度: {len(db_password)}, 包含字母: {any(c.isalpha() for c in db_password)}, 包含数字: {any(c.isdigit() for c in db_password)}"
    ))
    
    return security_checks

def validate_service_connectivity() -> List[Tuple[str, str, bool, str]]:
    """验证服务连接配置"""
    connectivity_checks = []
    
    # 检查GLM API密钥格式
    glm_key = os.getenv("GLM_API_KEY", "")
    is_valid_format = bool(re.match(r'^[a-f0-9]{32}\.[A-Za-z0-9]{16}$', glm_key))
    connectivity_checks.append((
        "GLM API密钥格式", 
        "GLM API密钥格式应为32位hex.16位字符", 
        is_valid_format,
        f"格式匹配: {is_valid_format}"
    ))
    
    # 检查数据库URL格式
    db_url = os.getenv("DATABASE_URL", "")
    is_valid_db_url = db_url.startswith("mysql+pymysql://") and "@" in db_url and "/" in db_url
    connectivity_checks.append((
        "数据库URL格式", 
        "数据库URL应为mysql+pymysql://格式", 
        is_valid_db_url,
        f"格式正确: {is_valid_db_url}"
    ))
    
    # 检查Redis URL格式
    redis_url = os.getenv("REDIS_URL", "")
    is_valid_redis_url = redis_url.startswith("redis://") and ":" in redis_url
    connectivity_checks.append((
        "Redis URL格式", 
        "Redis URL应为redis://格式", 
        is_valid_redis_url,
        f"格式正确: {is_valid_redis_url}"
    ))
    
    return connectivity_checks

def print_validation_results():
    """打印验证结果"""
    print("🔍 财经新闻Bot - 环境变量验证")
    print("=" * 60)
    
    # 加载环境变量
    env_path = os.path.join(project_root, ".env")
    env_vars = load_env_file(env_path)
    
    if not env_vars:
        print("❌ 未找到.env文件或文件为空")
        return False
    
    print(f"📁 环境文件: {env_path}")
    print(f"📊 配置项数量: {len(env_vars)}")
    print()
    
    all_passed = True
    
    # 验证必需变量
    print("🔴 必需环境变量检查:")
    required_results = validate_required_vars()
    for var_name, description, is_valid in required_results:
        status = "✅" if is_valid else "❌"
        print(f"  {status} {var_name}: {description}")
        if not is_valid:
            all_passed = False
    print()
    
    # 验证可选变量
    print("🟡 可选环境变量检查:")
    optional_results = validate_optional_vars()
    for var_name, description, is_set in optional_results:
        status = "✅" if is_set else "⚪"
        print(f"  {status} {var_name}: {description}")
    print()
    
    # 验证安全设置
    print("🔒 安全配置检查:")
    security_results = validate_security_settings()
    for check_name, description, is_secure, details in security_results:
        status = "✅" if is_secure else "⚠️"
        print(f"  {status} {check_name}: {description}")
        print(f"     {details}")
        if not is_secure:
            all_passed = False
    print()
    
    # 验证服务连接
    print("🌐 服务连接配置检查:")
    connectivity_results = validate_service_connectivity()
    for check_name, description, is_valid, details in connectivity_results:
        status = "✅" if is_valid else "⚠️"
        print(f"  {status} {check_name}: {description}")
        print(f"     {details}")
        if not is_valid:
            all_passed = False
    print()
    
    # 总结
    if all_passed:
        print("🎉 所有必需配置项验证通过！")
        print("💡 提示: 请确保所有服务（MySQL、Redis等）正常运行")
    else:
        print("❌ 发现配置问题，请检查上述标记的项目")
        print("📖 参考README.md获取详细配置说明")
    
    return all_passed

if __name__ == "__main__":
    # 设置环境变量（从.env文件加载）
    env_path = os.path.join(project_root, ".env")
    env_vars = load_env_file(env_path)
    for key, value in env_vars.items():
        os.environ[key] = value
    
    success = print_validation_results()
    sys.exit(0 if success else 1)
