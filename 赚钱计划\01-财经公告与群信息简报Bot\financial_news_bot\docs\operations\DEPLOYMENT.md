# 部署指南

本文档详细介绍了财经新闻Bot的部署方法和配置选项。

## 📋 部署前准备

### 系统要求

**最低配置**：
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB可用空间
- 操作系统: Linux/Windows/macOS

**推荐配置**：
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB可用空间
- 操作系统: Ubuntu 20.04+ / CentOS 8+

### 依赖服务

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+

## 🚀 Docker部署（推荐）

### 1. 快速部署

```bash
# 克隆项目
git clone <repository-url>
cd financial_news_bot

# 复制环境变量配置
cp .env.example .env

# 编辑配置文件
nano .env

# 启动所有服务
docker-compose up -d
```

### 2. 环境变量配置

编辑 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@mysql:3306/financial_news_bot
MYSQL_ROOT_PASSWORD=your-secure-password
MYSQL_DATABASE=financial_news_bot

# Redis配置
REDIS_URL=redis://redis:6379/0

# AI服务配置
GLM_API_KEY=your-glm-api-key-from-zhipu-ai

# 推送服务配置
WECHAT_WORK_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
FEISHU_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/your-hook-id
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# 安全配置
SECRET_KEY=your-very-secure-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=structured
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true

# 应用配置
ENVIRONMENT=production
DEBUG=false
```

### 3. Docker Compose配置

`docker-compose.yml` 主要服务：

```yaml
version: '3.8'

services:
  # 主应用
  app:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - GLM_API_KEY=${GLM_API_KEY}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:latest
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 4. 启动和管理

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down

# 重启服务
docker-compose restart app

# 更新服务
docker-compose pull
docker-compose up -d --force-recreate
```

## 🔧 手动部署

### 1. 环境准备

```bash
# 安装Python 3.9+
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-venv

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd financial_news_bot/backend

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 数据库设置

```bash
# 安装MySQL
sudo apt install mysql-server

# 创建数据库
mysql -u root -p
CREATE DATABASE financial_news_bot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'fnb_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON financial_news_bot.* TO 'fnb_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. Redis设置

```bash
# 安装Redis
sudo apt install redis-server

# 启动Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 5. 应用配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置
nano .env

# 数据库迁移
alembic upgrade head
```

### 6. 启动应用

```bash
# 开发模式
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 🌐 生产环境部署

### 1. 使用Nginx反向代理

安装Nginx：
```bash
sudo apt install nginx
```

配置文件 `/etc/nginx/sites-available/financial-news-bot`：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static/ {
        alias /path/to/static/files/;
    }

    # 日志文件
    access_log /var/log/nginx/fnb_access.log;
    error_log /var/log/nginx/fnb_error.log;
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/financial-news-bot /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2. SSL证书配置

使用Let's Encrypt：
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 系统服务配置

创建systemd服务文件 `/etc/systemd/system/financial-news-bot.service`：
```ini
[Unit]
Description=Financial News Bot API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/financial_news_bot/backend
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable financial-news-bot
sudo systemctl start financial-news-bot
```

## 📊 监控和日志

### 1. 日志管理

日志文件位置：
- 应用日志: `/app/logs/financial_news_bot.log`
- 错误日志: `/app/logs/financial_news_bot_error.log`
- Nginx日志: `/var/log/nginx/`

日志轮转配置 `/etc/logrotate.d/financial-news-bot`：
```
/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload financial-news-bot
    endscript
}
```

### 2. 监控设置

健康检查脚本：
```bash
#!/bin/bash
# health_check.sh

URL="http://localhost:8000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

添加到crontab：
```bash
# 每5分钟检查一次
*/5 * * * * /path/to/health_check.sh
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 允许SSH
sudo ufw allow ssh

# 允许HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 2. 数据库安全

```bash
# MySQL安全配置
sudo mysql_secure_installation

# 限制数据库访问
# 编辑 /etc/mysql/mysql.conf.d/mysqld.cnf
bind-address = 127.0.0.1
```

### 3. 应用安全

- 使用强密码和密钥
- 定期更新依赖包
- 启用HTTPS
- 配置适当的CORS策略
- 实施API限流

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   sudo systemctl status mysql
   
   # 检查连接
   mysql -u root -p -e "SELECT 1"
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   sudo systemctl status redis-server
   
   # 测试连接
   redis-cli ping
   ```

3. **应用启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs app
   
   # 或者
   journalctl -u financial-news-bot -f
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 检查进程
   ps aux --sort=-%mem | head
   ```

### 性能优化

1. **数据库优化**
   - 配置适当的缓冲池大小
   - 添加必要的索引
   - 定期清理日志

2. **应用优化**
   - 调整worker数量
   - 配置连接池
   - 启用缓存

3. **系统优化**
   - 调整文件描述符限制
   - 配置内核参数
   - 使用SSD存储

## 📈 扩展部署

### 负载均衡

使用Nginx配置负载均衡：
```nginx
upstream financial_news_bot {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://financial_news_bot;
    }
}
```

### 数据库集群

配置MySQL主从复制或使用MySQL Cluster。

### 缓存集群

配置Redis Cluster或使用Redis Sentinel。

---

更多信息请参考：
- [API参考文档](API_REFERENCE.md)
- [开发指南](DEVELOPMENT.md)
- [故障排除](TROUBLESHOOTING.md)
