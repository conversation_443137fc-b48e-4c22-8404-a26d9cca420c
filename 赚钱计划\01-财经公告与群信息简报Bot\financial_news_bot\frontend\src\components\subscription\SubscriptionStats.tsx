import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Progress,
  Tag,
  Tooltip,
  message,
} from 'antd';
import {
  BarChartOutlined,
  DownloadOutlined,
  EyeOutlined,
  SendOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface PushRecord {
  id: string;
  title: string;
  channel: string;
  status: 'success' | 'failed' | 'pending';
  sent_at: string;
  opened: boolean;
  clicked: boolean;
  news_count: number;
}

interface SubscriptionStatsProps {
  subscriptionId: string;
}

const SubscriptionStats: React.FC<SubscriptionStatsProps> = ({ subscriptionId: _subscriptionId }) => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [channel, setChannel] = useState<string>('all');

  // 真实统计数据状态
  const [stats, setStats] = useState({
    totalPushes: 0,
    successRate: 0,
    openRate: 0,
    clickRate: 0,
    avgNewsPerPush: 0,
    totalNewsDelivered: 0,
    loading: true,
  });

  // 真实推送记录状态
  const [pushRecords, setPushRecords] = useState<PushRecord[]>([]);
  const [recordsLoading, setRecordsLoading] = useState(true);

  // 加载真实统计数据
  const loadStats = async () => {
    try {
      setStats(prev => ({ ...prev, loading: true }));

      const response = await fetch('/api/v1/push/analytics/overview', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats({
          totalPushes: data.total_pushes || 0,
          successRate: data.success_rate || 0,
          openRate: data.open_rate || 0,
          clickRate: data.click_rate || 0,
          avgNewsPerPush: data.avg_news_per_push || 0,
          totalNewsDelivered: data.total_news_delivered || 0,
          loading: false,
        });
      } else {
        // 统计数据获取失败日志 - 用于监控数据问题
        console.error('获取统计数据失败');
        setStats(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      // 统计数据加载失败日志 - 用于错误追踪
      console.error('加载统计数据失败:', error);
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  // 加载真实推送记录
  const loadPushRecords = async () => {
    try {
      setRecordsLoading(true);

      const params = new URLSearchParams({
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        ...(channel !== 'all' && { channel }),
      });

      const response = await fetch(`/api/v1/push/analytics/records?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPushRecords(data.items || []);
      } else {
        // 推送记录获取失败日志 - 用于监控推送问题
        console.error('获取推送记录失败');
        setPushRecords([]);
      }
    } catch (error) {
      // 推送记录加载失败日志 - 用于错误追踪
      console.error('加载推送记录失败:', error);
      setPushRecords([]);
    } finally {
      setRecordsLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    loadStats();
    loadPushRecords();
  }, []);

  // 当日期范围或渠道改变时重新加载数据
  useEffect(() => {
    loadPushRecords();
  }, [dateRange, channel]);

  const columns = [
    {
      title: '推送标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: PushRecord) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.news_count} 条新闻
          </Text>
        </div>
      ),
    },
    {
      title: '推送渠道',
      dataIndex: 'channel',
      key: 'channel',
      render: (channel: string) => {
        const channelMap = {
          email: { text: '邮箱', color: 'blue' },
          wechat: { text: '企业微信', color: 'green' },
          feishu: { text: '飞书', color: 'purple' },
          webhook: { text: 'Webhook', color: 'orange' },
        };
        const config = channelMap[channel as keyof typeof channelMap] || { text: channel, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          success: { text: '成功', color: 'success', icon: <CheckCircleOutlined /> },
          failed: { text: '失败', color: 'error', icon: <ClockCircleOutlined /> },
          pending: { text: '发送中', color: 'processing', icon: <SendOutlined /> },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '互动情况',
      key: 'interaction',
      render: (record: PushRecord) => (
        <Space>
          <Tooltip title={record.opened ? '已打开' : '未打开'}>
            <EyeOutlined style={{ color: record.opened ? '#52c41a' : '#d9d9d9' }} />
          </Tooltip>
          <Tooltip title={record.clicked ? '已点击' : '未点击'}>
            <BarChartOutlined style={{ color: record.clicked ? '#1890ff' : '#d9d9d9' }} />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '发送时间',
      dataIndex: 'sent_at',
      key: 'sent_at',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
    },
  ];

  const handleExport = async () => {
    try {
      // 真实导出功能 - 调用后端API生成报告
      const params = new URLSearchParams({
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        ...(channel !== 'all' && { channel }),
        format: 'excel', // 或 'csv'
      });

      const response = await fetch(`/api/v1/push/analytics/export?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        // 下载文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `推送统计报告_${dateRange[0].format('YYYY-MM-DD')}_${dateRange[1].format('YYYY-MM-DD')}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        message.success('报告导出成功');
      } else {
        message.error('导出失败，请稍后重试');
      }
    } catch (error) {
      // 报告导出失败日志 - 用于错误追踪
      console.error('导出失败:', error);
      message.error('导出失败，请检查网络连接');
    }
  };

  return (
    <div>
      <Title level={4}>订阅统计</Title>

      {/* 筛选条件 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col>
            <Text>时间范围：</Text>
            <RangePicker
              value={dateRange}
              onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
              style={{ marginLeft: '8px' }}
            />
          </Col>
          <Col>
            <Text>推送渠道：</Text>
            <Select
              value={channel}
              onChange={setChannel}
              style={{ width: 120, marginLeft: '8px' }}
            >
              <Option value="all">全部</Option>
              <Option value="email">邮箱</Option>
              <Option value="wechat">企业微信</Option>
              <Option value="feishu">飞书</Option>
              <Option value="webhook">Webhook</Option>
            </Select>
          </Col>
          <Col>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出报告
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={stats.loading}>
            <Statistic
              title="总推送次数"
              value={stats.totalPushes}
              prefix={<SendOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={stats.loading}>
            <Statistic
              title="成功率"
              value={stats.successRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress percent={stats.successRate} showInfo={false} strokeColor="#52c41a" />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="打开率"
              value={stats.openRate}
              suffix="%"
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <Progress percent={stats.openRate} showInfo={false} strokeColor="#faad14" />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="点击率"
              value={stats.clickRate}
              suffix="%"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <Progress percent={stats.clickRate} showInfo={false} strokeColor="#722ed1" />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={12}>
          <Card title="推送概览" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>平均每次推送新闻数：</Text>
                <Text strong>{stats.avgNewsPerPush} 条</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>累计推送新闻总数：</Text>
                <Text strong>{stats.totalNewsDelivered} 条</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>最近推送时间：</Text>
                <Text>{dayjs(pushRecords[0]?.sent_at).format('MM-DD HH:mm')}</Text>
              </div>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="渠道分布" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>邮箱：</Text>
                <div style={{ flex: 1, marginLeft: '16px' }}>
                  <Progress percent={45} showInfo={false} size="small" />
                </div>
                <Text style={{ marginLeft: '8px' }}>45%</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>企业微信：</Text>
                <div style={{ flex: 1, marginLeft: '16px' }}>
                  <Progress percent={30} showInfo={false} size="small" strokeColor="#52c41a" />
                </div>
                <Text style={{ marginLeft: '8px' }}>30%</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>飞书：</Text>
                <div style={{ flex: 1, marginLeft: '16px' }}>
                  <Progress percent={25} showInfo={false} size="small" strokeColor="#722ed1" />
                </div>
                <Text style={{ marginLeft: '8px' }}>25%</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 推送历史 */}
      <Card title="推送历史" size="small">
        <Table
          columns={columns}
          dataSource={pushRecords}
          rowKey="id"
          loading={recordsLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );
};

export default SubscriptionStats;
