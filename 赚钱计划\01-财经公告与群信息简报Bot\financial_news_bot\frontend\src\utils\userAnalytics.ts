/**
 * 用户行为数据收集和分析工具
 * 收集用户交互数据、页面访问、功能使用等信息用于产品优化
 */

// 事件类型定义
export interface AnalyticsEvent {
  id: string;
  type: 'page_view' | 'click' | 'form_submit' | 'search' | 'scroll' | 'time_spent' | 'feature_use';
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties?: Record<string, unknown>;
  timestamp: number;
  sessionId: string;
  userId?: string;
  url: string;
  referrer?: string;
  userAgent: string;
  viewport: { width: number; height: number };
}

// 用户会话信息
export interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  endTime?: number;
  pageViews: number;
  events: number;
  device: {
    type: 'desktop' | 'tablet' | 'mobile';
    os: string;
    browser: string;
  };
  location?: {
    country?: string;
    city?: string;
  };
}

// 热力图数据
export interface HeatmapData {
  x: number;
  y: number;
  value: number;
  element?: string;
  timestamp: number;
}

// 漏斗分析数据
export interface FunnelStep {
  name: string;
  users: number;
  conversionRate: number;
  dropOffRate: number;
}

class UserAnalytics {
  private sessionId: string;
  private userId?: string;
  private events: AnalyticsEvent[] = [];
  private heatmapData: HeatmapData[] = [];
  private sessionStartTime: number;
  private lastActivityTime: number;
  private pageStartTime: number;
  private isEnabled = true;
  private reportEndpoint = '/api/v1/analytics/events';
  private batchSize = 50;
  private reportInterval = 30000; // 30秒

  constructor() {
    this.sessionId = this.generateSessionId();
    this.sessionStartTime = Date.now();
    this.lastActivityTime = Date.now();
    this.pageStartTime = Date.now();
    
    this.init();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private init() {
    if (!this.isEnabled) return;

    // 页面访问跟踪
    this.trackPageView();

    // 点击事件跟踪
    this.initClickTracking();

    // 滚动事件跟踪
    this.initScrollTracking();

    // 表单提交跟踪
    this.initFormTracking();

    // 页面停留时间跟踪
    this.initTimeTracking();

    // 热力图数据收集
    this.initHeatmapTracking();

    // 定期上报数据
    this.startReporting();

    // 页面卸载时上报
    this.initUnloadTracking();
  }

  private trackPageView() {
    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      type: 'page_view',
      category: 'navigation',
      action: 'page_view',
      label: document.title,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      url: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      properties: {
        path: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
      },
    };

    this.recordEvent(event);
  }

  private initClickTracking() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const element = this.getElementInfo(target);

      const analyticsEvent: AnalyticsEvent = {
        id: this.generateEventId(),
        type: 'click',
        category: 'interaction',
        action: 'click',
        label: element.text || element.selector,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        userId: this.userId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        properties: {
          element: element.selector,
          text: element.text,
          x: event.clientX,
          y: event.clientY,
          button: event.button,
        },
      };

      this.recordEvent(analyticsEvent);

      // 记录热力图数据
      this.recordHeatmapData({
        x: event.clientX,
        y: event.clientY,
        value: 1,
        element: element.selector,
        timestamp: Date.now(),
      });

      this.updateLastActivity();
    });
  }

  private initScrollTracking() {
    let scrollDepth = 0;
    let maxScrollDepth = 0;

    const trackScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      
      if (documentHeight > 0) {
        scrollDepth = Math.round((scrollTop / documentHeight) * 100);
        
        if (scrollDepth > maxScrollDepth) {
          maxScrollDepth = scrollDepth;
          
          // 记录滚动里程碑
          if (maxScrollDepth >= 25 && maxScrollDepth % 25 === 0) {
            const event: AnalyticsEvent = {
              id: this.generateEventId(),
              type: 'scroll',
              category: 'engagement',
              action: 'scroll_depth',
              label: `${maxScrollDepth}%`,
              value: maxScrollDepth,
              timestamp: Date.now(),
              sessionId: this.sessionId,
              userId: this.userId,
              url: window.location.href,
              userAgent: navigator.userAgent,
              viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
              },
            };
            
            this.recordEvent(event);
          }
        }
      }

      this.updateLastActivity();
    };

    // 节流处理
    let scrollTimer: NodeJS.Timeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(trackScroll, 100);
    });
  }

  private initFormTracking() {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formInfo = this.getElementInfo(form);

      const analyticsEvent: AnalyticsEvent = {
        id: this.generateEventId(),
        type: 'form_submit',
        category: 'conversion',
        action: 'form_submit',
        label: formInfo.selector,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        userId: this.userId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        properties: {
          formId: form.id,
          formAction: form.action,
          formMethod: form.method,
          fieldCount: form.elements.length,
        },
      };

      this.recordEvent(analyticsEvent);
      this.updateLastActivity();
    });
  }

  private initTimeTracking() {
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackTimeSpent();
      } else {
        this.pageStartTime = Date.now();
      }
    });

    // 定期记录活跃时间
    setInterval(() => {
      if (!document.hidden && this.isUserActive()) {
        this.trackTimeSpent();
        this.pageStartTime = Date.now();
      }
    }, 30000); // 每30秒记录一次
  }

  private initHeatmapTracking() {
    // 鼠标移动跟踪（采样）
    let mouseMoveCount = 0;
    document.addEventListener('mousemove', (event) => {
      mouseMoveCount++;
      
      // 每100次鼠标移动记录一次
      if (mouseMoveCount % 100 === 0) {
        this.recordHeatmapData({
          x: event.clientX,
          y: event.clientY,
          value: 0.1,
          timestamp: Date.now(),
        });
      }
    });
  }

  private initUnloadTracking() {
    window.addEventListener('beforeunload', () => {
      this.trackTimeSpent();
      this.reportData(true); // 同步上报
    });
  }

  private trackTimeSpent() {
    const timeSpent = Date.now() - this.pageStartTime;
    
    if (timeSpent > 1000) { // 至少停留1秒
      const event: AnalyticsEvent = {
        id: this.generateEventId(),
        type: 'time_spent',
        category: 'engagement',
        action: 'time_on_page',
        label: window.location.pathname,
        value: timeSpent,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        userId: this.userId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      };

      this.recordEvent(event);
    }
  }

  private getElementInfo(element: HTMLElement) {
    const selector = this.getElementSelector(element);
    const text = element.textContent?.trim().substring(0, 100) || '';
    
    return { selector, text };
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.trim());
      if (classes.length > 0) {
        return `.${classes[0]}`;
      }
    }
    
    // 使用data-testid属性
    const testId = element.getAttribute('data-testid');
    if (testId) {
      return `[data-testid="${testId}"]`;
    }
    
    return element.tagName.toLowerCase();
  }

  private isUserActive(): boolean {
    return Date.now() - this.lastActivityTime < 60000; // 1分钟内有活动
  }

  private updateLastActivity() {
    this.lastActivityTime = Date.now();
  }

  private recordEvent(event: AnalyticsEvent) {
    this.events.push(event);
    
    // 限制内存使用
    if (this.events.length > 1000) {
      this.events = this.events.slice(-500);
    }
  }

  private recordHeatmapData(data: HeatmapData) {
    this.heatmapData.push(data);
    
    // 限制内存使用
    if (this.heatmapData.length > 1000) {
      this.heatmapData = this.heatmapData.slice(-500);
    }
  }

  private startReporting() {
    setInterval(() => {
      this.reportData();
    }, this.reportInterval);
  }

  private async reportData(isSync = false) {
    if (this.events.length === 0) return;

    const batch = this.events.splice(0, this.batchSize);
    const heatmapBatch = this.heatmapData.splice(0, this.batchSize);

    const payload = {
      sessionId: this.sessionId,
      userId: this.userId,
      events: batch,
      heatmapData: heatmapBatch,
      timestamp: Date.now(),
    };

    try {
      if (isSync && navigator.sendBeacon) {
        navigator.sendBeacon(this.reportEndpoint, JSON.stringify(payload));
      } else {
        await fetch(this.reportEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });
      }
    } catch (error) {
      // Analytics报告失败日志 - 用于监控数据上报问题
      console.error('Analytics报告失败:', error);
      // 失败的事件重新加入队列
      this.events.unshift(...batch);
    }
  }

  // 公共方法
  public setUserId(userId: string) {
    this.userId = userId;
  }

  public trackCustomEvent(
    category: string,
    action: string,
    label?: string,
    value?: number,
    properties?: Record<string, unknown>
  ) {
    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      type: 'feature_use',
      category,
      action,
      label,
      value,
      properties,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    };

    this.recordEvent(event);
  }

  public trackSearch(query: string, results: number) {
    this.trackCustomEvent('search', 'search_query', query, results, {
      query,
      resultsCount: results,
    });
  }

  public trackFeatureUse(feature: string, action: string, context?: Record<string, unknown>) {
    this.trackCustomEvent('feature', action, feature, undefined, context);
  }

  public getSessionInfo(): UserSession {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      startTime: this.sessionStartTime,
      endTime: this.lastActivityTime,
      pageViews: this.events.filter(e => e.type === 'page_view').length,
      events: this.events.length,
      device: this.getDeviceInfo(),
    };
  }

  private getDeviceInfo() {
    const userAgent = navigator.userAgent;
    let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop';
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      deviceType = /iPad/.test(userAgent) ? 'tablet' : 'mobile';
    }

    return {
      type: deviceType,
      os: this.getOS(),
      browser: this.getBrowser(),
    };
  }

  private getOS(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  public enable() {
    this.isEnabled = true;
  }

  public disable() {
    this.isEnabled = false;
  }
}

// 全局分析实例
export const userAnalytics = new UserAnalytics();

// 便捷方法
export const trackEvent = (category: string, action: string, label?: string, value?: number) => {
  userAnalytics.trackCustomEvent(category, action, label, value);
};

export const trackSearch = (query: string, results: number) => {
  userAnalytics.trackSearch(query, results);
};

export const trackFeature = (feature: string, action: string, context?: Record<string, unknown>) => {
  userAnalytics.trackFeatureUse(feature, action, context);
};

export const setAnalyticsUserId = (userId: string) => {
  userAnalytics.setUserId(userId);
};
