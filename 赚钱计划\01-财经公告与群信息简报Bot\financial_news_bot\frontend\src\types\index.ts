// 通用类型定义

// 扩展CSS样式类型以支持webkit属性
declare global {
  interface CSSStyleDeclaration {
    webkitOverflowScrolling?: string;
    overflowScrolling?: string;
  }
}

// API响应基础类型
export interface ApiResponse<T = unknown> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
  request_id?: string;
}

// 分页响应类型
export interface PaginatedResponse<T = unknown> {
  code: number;
  message: string;
  data: {
    items: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  };
  timestamp: string;
  request_id?: string;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  full_name?: string;
  company?: string;
  position?: string;
  is_active: boolean;
  is_verified: boolean;
  subscription_type: 'free' | 'basic' | 'advanced' | 'premium';
  subscription_expires_at?: string;
  created_at: string;
  updated_at: string;
}

// 登录请求类型
export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

// 注册请求类型
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  phone?: string;
  full_name?: string;
  verification_code?: string;
}

// 认证响应类型
export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// 新闻相关类型
export interface News {
  id: number;
  title: string;
  content: string;
  summary?: string;
  source: string;
  source_url?: string;
  category: string;
  tags: string[];
  importance_score: number;
  sentiment_score?: number;
  published_at: string;
  created_at: string;
  updated_at: string;
  is_bookmarked?: boolean;
  view_count?: number;
  share_count?: number;
}

// 新闻搜索参数
export interface NewsSearchParams {
  keyword?: string;
  category?: string;
  source?: string;
  start_date?: string;
  end_date?: string;
  importance_min?: number;
  importance_max?: number;
  page?: number;
  limit?: number;
  sort_by?: 'published_at' | 'importance_score' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

// 订阅相关类型
export interface Subscription {
  id: number;
  name: string;
  description?: string;
  keywords: string[];
  categories: string[];
  sources: string[];
  importance_threshold: number;
  push_channels: PushChannel[];
  push_schedule: PushSchedule;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_push_at?: string;
  push_count: number;
}

// 推送渠道类型
export interface PushChannel {
  type: 'wechat_group' | 'wechat_work' | 'email' | 'feishu';
  target: string;
  config: Record<string, unknown>;
  is_active: boolean;
}

// 推送计划类型
export interface PushSchedule {
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
  time_slots: string[];
  weekdays: number[];
  timezone: string;
}

// 订阅创建/更新请求
export interface SubscriptionRequest {
  name: string;
  description?: string;
  keywords: string[];
  categories: string[];
  sources: string[];
  importance_threshold: number;
  push_channels: PushChannel[];
  push_schedule: PushSchedule;
  is_active?: boolean;
}

// 推送统计类型
export interface PushStats {
  total_sent: number;
  total_success: number;
  total_failed: number;
  success_rate: number;
  channels: Record<string, {
    total_sent: number;
    total_success: number;
    total_failed: number;
    success_rate: number;
  }>;
  daily_breakdown: Record<string, {
    total: number;
    success: number;
    failed: number;
    success_rate: number;
  }>;
}

// 用户行为事件类型
export interface UserBehaviorEvent {
  event_type: 'push_sent' | 'push_opened' | 'push_clicked' | 'news_viewed' | 'news_shared' | 'news_bookmarked';
  event_data: Record<string, unknown>;
  session_id?: string;
}

// 用户行为摘要
export interface UserBehaviorSummary {
  user_id: number;
  period: {
    days: number;
    start_date: string;
    end_date: string;
  };
  events: Record<string, number>;
  engagement: {
    push_open_rate: number;
    push_click_rate: number;
    news_view_count: number;
    active_days: number;
  };
  preferences: {
    most_active_hour: number;
    preferred_channels: string[];
    top_categories: string[];
  };
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (_rule: unknown, _value: unknown) => Promise<void>;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
  permission?: string;
}

// 面包屑类型
export interface BreadcrumbItem {
  title: string;
  path?: string;
}

// 通知类型
export interface NotificationItem {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  action?: {
    text: string;
    url: string;
  };
}

// 主题配置类型
export interface ThemeConfig {
  primaryColor: string;
  darkMode: boolean;
  compactMode: boolean;
  borderRadius: number;
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: ThemeConfig;
  language: 'zh-CN' | 'en-US';
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    profile_public: boolean;
    activity_tracking: boolean;
    data_analytics: boolean;
  };
}
