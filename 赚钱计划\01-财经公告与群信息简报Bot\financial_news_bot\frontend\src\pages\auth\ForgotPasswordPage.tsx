import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Typography,
  Alert,
  Steps,
  message,
  Row,
  Col,
} from 'antd';
import {
  MailOutlined,
  LockOutlined,
  SafetyOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

// import { useAppDispatch } from '../../store'; // 暂时未使用，保留备用
import { authService } from '../../services/authService';

const { Title, Text } = Typography;
const { Step } = Steps;

const ForgotPasswordPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  // const dispatch = useAppDispatch(); // 暂时未使用，保留备用

  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  // const [verificationCode, setVerificationCode] = useState(''); // 暂时未使用，保留备用
  const [countdown, setCountdown] = useState(0);

  // 步骤1：输入邮箱
  const handleSendCode = async (values: { email: string }) => {
    setLoading(true);
    try {
      await authService.forgotPassword(values.email);
      setEmail(values.email);
      setCurrentStep(1);
      setCountdown(60);
      message.success('验证码已发送到您的邮箱');

      // 倒计时
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error: unknown) {
      message.error((error as Error).message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  // 步骤2：验证码和新密码
  const handleResetPassword = async (values: { code: string; new_password: string }) => {
    setLoading(true);
    try {
      await authService.resetPassword({
        token: values.code,
        new_password: values.new_password,
      });
      setCurrentStep(2);
      message.success('密码重置成功');
    } catch (error: unknown) {
      message.error((error as Error).message || '密码重置失败');
    } finally {
      setLoading(false);
    }
  };

  // 重新发送验证码
  const handleResendCode = async () => {
    if (countdown > 0) return;

    setLoading(true);
    try {
      await authService.forgotPassword(email);
      setCountdown(60);
      message.success('验证码已重新发送');

      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error: unknown) {
      message.error((error as Error).message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    {
      title: '输入邮箱',
      description: '输入您的注册邮箱',
    },
    {
      title: '验证身份',
      description: '输入验证码和新密码',
    },
    {
      title: '重置完成',
      description: '密码重置成功',
    },
  ];

  return (
    <div style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <Title level={2} style={{ color: '#1890ff', marginBottom: '0.5rem' }}>
          重置密码
        </Title>
        <Text type="secondary">
          请按照以下步骤重置您的密码
        </Text>
      </div>

      {/* 步骤指示器 */}
      <Steps current={currentStep} style={{ marginBottom: '2rem' }}>
        {steps.map((step, index) => (
          <Step
            key={index}
            title={step.title}
            description={step.description}
            icon={currentStep === 2 && index === 2 ? <CheckCircleOutlined /> : undefined}
          />
        ))}
      </Steps>

      {/* 步骤1：输入邮箱 */}
      {currentStep === 0 && (
        <Form
          form={form}
          name="forgot-password-email"
          onFinish={handleSendCode}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="请输入您的注册邮箱"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              发送验证码
            </Button>
          </Form.Item>
        </Form>
      )}

      {/* 步骤2：验证码和新密码 */}
      {currentStep === 1 && (
        <Form
          name="reset-password"
          onFinish={handleResetPassword}
          autoComplete="off"
          size="large"
          layout="vertical"
        >
          <Alert
            message={`验证码已发送到 ${email}`}
            type="info"
            showIcon
            style={{ marginBottom: '1rem' }}
          />

          <Form.Item
            name="code"
            label="验证码"
            rules={[
              { required: true, message: '请输入验证码' },
              { len: 6, message: '验证码为6位数字' },
            ]}
          >
            <Row gutter={8}>
              <Col span={16}>
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入6位验证码"
                  maxLength={6}
                />
              </Col>
              <Col span={8}>
                <Button
                  onClick={handleResendCode}
                  disabled={countdown > 0}
                  loading={loading}
                  block
                >
                  {countdown > 0 ? `${countdown}s` : '重新发送'}
                </Button>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入新密码"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            name="confirm_password"
            label="确认新密码"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              重置密码
            </Button>
          </Form.Item>
        </Form>
      )}

      {/* 步骤3：重置完成 */}
      {currentStep === 2 && (
        <div style={{ textAlign: 'center', padding: '2rem 0' }}>
          <CheckCircleOutlined
            style={{ fontSize: '4rem', color: '#52c41a', marginBottom: '1rem' }}
          />
          <Title level={3} style={{ color: '#52c41a' }}>
            密码重置成功！
          </Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: '2rem' }}>
            您的密码已成功重置，请使用新密码登录
          </Text>
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/auth/login')}
          >
            立即登录
          </Button>
        </div>
      )}

      {/* 返回登录 */}
      {currentStep < 2 && (
        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <Link to="/auth/login">
            <Button type="link" icon={<ArrowLeftOutlined />}>
              返回登录
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default ForgotPasswordPage;
