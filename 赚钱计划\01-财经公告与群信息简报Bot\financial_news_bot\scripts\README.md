# 运维脚本工具集说明

本目录包含财经新闻Bot项目的运维管理脚本。

## 📁 脚本概览

### 🚀 部署管理
- **`deploy-manager.py`** - 部署管理工具
- **`config-manager.py`** - 配置管理工具

### 🔒 安全审计
- **`security-audit.sh`** - 安全审计脚本

## 🔧 使用指南

### 部署管理

```bash
# 启动服务
python scripts/deploy-manager.py start

# 停止服务
python scripts/deploy-manager.py stop

# 重启服务
python scripts/deploy-manager.py restart

# 查看状态
python scripts/deploy-manager.py status

# 查看日志
python scripts/deploy-manager.py logs

# 更新服务
python scripts/deploy-manager.py update

# 备份数据
python scripts/deploy-manager.py backup

# 健康检查
python scripts/deploy-manager.py health
```

### 配置管理

```bash
# 生成安全配置
python scripts/config-manager.py generate

# 验证配置
python scripts/config-manager.py validate

# 显示配置模板
python scripts/config-manager.py template

# 备份配置
python scripts/config-manager.py backup

# 恢复配置
python scripts/config-manager.py restore --backup-file .env.backup.20240101_120000

# 列出备份
python scripts/config-manager.py list-backups

# 检查权限
python scripts/config-manager.py check-permissions
```

### 安全审计

```bash
# 运行安全审计
bash scripts/security-audit.sh
```

## 🎯 运维最佳实践

### 生产环境维护

1. **日常维护**：
   ```bash
   python scripts/deploy-manager.py health
   python scripts/deploy-manager.py backup
   ```

2. **配置管理**：
   ```bash
   python scripts/config-manager.py backup
   python scripts/config-manager.py validate
   ```

3. **安全检查**：
   ```bash
   bash scripts/security-audit.sh
   ```

## 🔍 故障排除

### 常见问题

1. **脚本执行权限问题**：
   ```bash
   chmod +x scripts/*.py scripts/*.sh
   ```

2. **Docker问题**：
   ```bash
   python scripts/deploy-manager.py health
   ```

### 日志查看

- 部署日志：`docker-compose logs`
- 系统日志：通过deploy-manager.py查看

---

**注意**：所有脚本都支持 `--help` 参数查看详细使用说明。
