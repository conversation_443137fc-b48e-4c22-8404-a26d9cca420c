@echo off
chcp 65001 >nul
title 财经新闻Bot - 开发环境管理

echo.
echo ========================================
echo 财经新闻Bot - 开发环境管理
echo ========================================
echo.

if "%1"=="" goto :show_help

if "%1"=="setup" goto :setup
if "%1"=="start" goto :start
if "%1"=="stop" goto :stop
if "%1"=="restart" goto :restart
if "%1"=="status" goto :status
if "%1"=="logs" goto :logs
if "%1"=="build" goto :build
if "%1"=="migrate" goto :migrate
if "%1"=="test" goto :test
if "%1"=="health" goto :health
if "%1"=="admin" goto :admin
if "%1"=="help" goto :show_help

echo ❌ 未知命令: %1
goto :show_help

:setup
echo 🎯 开始完整环境设置...
python scripts/dev-start.py setup
goto :end

:start
echo 🚀 启动服务...
docker-compose up -d
goto :end

:stop
echo 🛑 停止服务...
docker-compose down
goto :end

:restart
echo 🔄 重启服务...
docker-compose restart
goto :end

:status
echo 📊 服务状态:
docker-compose ps
goto :end

:logs
echo 📋 显示服务日志...
if "%2"=="-f" (
    docker-compose logs -f
) else (
    docker-compose logs
)
goto :end

:build
echo 🏗️ 构建Docker服务...
docker-compose build
goto :end

:migrate
echo 🗄️ 执行数据库迁移...
docker-compose exec backend alembic upgrade head
goto :end

:test
echo 🧪 运行测试...
python scripts/dev-start.py test
goto :end

:health
echo 🏥 执行健康检查...
python scripts/dev-start.py health
goto :end

:admin
echo 👤 创建管理员用户...
python scripts/dev-start.py admin
goto :end

:show_help
echo 使用方法: dev-start.bat [命令]
echo.
echo 可用命令:
echo   setup     - 完整环境设置（推荐首次使用）
echo   start     - 启动所有服务
echo   stop      - 停止所有服务
echo   restart   - 重启所有服务
echo   status    - 显示服务状态
echo   logs      - 显示服务日志 (使用 -f 参数跟踪日志)
echo   build     - 构建Docker镜像
echo   migrate   - 执行数据库迁移
echo   test      - 运行测试
echo   health    - 健康检查
echo   admin     - 创建管理员用户
echo   help      - 显示此帮助信息
echo.
echo 示例:
echo   dev-start.bat setup     # 首次设置环境
echo   dev-start.bat start     # 启动服务
echo   dev-start.bat logs -f   # 跟踪日志
echo   dev-start.bat stop      # 停止服务
echo.
echo 🌐 访问地址:
echo   前端应用: http://localhost:8080
echo   API文档:  http://localhost:8080/docs
echo   健康检查: http://localhost:8080/health
goto :end

:end
echo.
pause
