from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import pymysql
import logging

# 导入统一日志配置
from app.utils.logging_config import setup_application_logging, RequestLoggingMiddleware
from app.utils.alert_system import alert_manager
# 导入全局异常处理器
from app.exceptions.global_handler import setup_exception_handlers

from .routers import (
    user, tasks, news, templates, rules, # analytics,  # 临时禁用
    scheduler, monitoring, security, infrastructure, admin,
    # 第4周新增路由（保留非推送相关）
    reports, subscription_with_permissions
)
# 第5周新增路由（保留非推送相关）
from .routers import user_behavior
# 统一推送服务路由
from .routers import unified_push_router
# AI服务路由
from .routers import ai_router
# 集成监控服务路由
from .routers import monitoring_router
# 数据处理管道路由
from .routers import data_processing_router
# 简化爬虫服务路由
from .routers import crawler_router
# 告警管理路由
from .routers import alert_router
from .middleware.rate_limiter import rate_limiter
from .middleware.response_middleware import ResponseMiddleware, RequestContextMiddleware

# 初始化日志系统
logging_config = setup_application_logging(
    service_name="financial_news_bot",
    level=os.getenv("LOG_LEVEL", "INFO"),
    format_type=os.getenv("LOG_FORMAT", "structured")
)

logger = logging.getLogger(__name__)

app = FastAPI(
    title="财经新闻Bot API",
    description="智能财经新闻推送系统API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加响应中间件
app.add_middleware(ResponseMiddleware)
app.add_middleware(RequestContextMiddleware)

# 注册路由
app.include_router(user.router, prefix="/api/v1")
app.include_router(tasks.router, prefix="/api/v1")
app.include_router(news.router, prefix="/api/v1")
app.include_router(templates.router, prefix="/api/v1")
app.include_router(rules.router, prefix="/api/v1")
# app.include_router(analytics.router, prefix="/api/v1")  # 临时禁用
app.include_router(scheduler.router, prefix="/api/v1")
app.include_router(monitoring.router, prefix="/api/v1")
app.include_router(security.router, prefix="/api/v1")
app.include_router(infrastructure.router, prefix="/api/v1")
app.include_router(admin.router, prefix="/api/v1")

# 第4周新增路由（保留非推送相关的路由）
app.include_router(reports.router, prefix="/api/v1")
app.include_router(subscription_with_permissions.router, prefix="/api/v1")

# 第5周新增路由（保留非推送相关的路由）
app.include_router(user_behavior.router, prefix="/api/v1")

# 注意：以下推送相关路由已被统一推送服务替代，已移除：
# - push_with_permissions.router (功能已集成到unified_push_router)
# - push_management.router (功能已集成到unified_push_router)
# - push_monitoring.router (功能已集成到unified_push_router)
# - push_analytics.router (功能已集成到unified_push_router)
# - enhanced_push.router (功能已集成到unified_push_router)

# 统一推送服务路由（整合所有推送功能）
app.include_router(unified_push_router.router)

# AI服务路由（激活GLM-4.5 Flash服务）
app.include_router(ai_router.router)

# 集成监控服务路由（替代独立微服务）
app.include_router(monitoring_router.router)

# 数据处理管道路由（重构后的数据处理服务）
app.include_router(data_processing_router.router)

# 简化爬虫服务路由（简化后的爬虫系统）
app.include_router(crawler_router.router)

# 告警管理路由（统一告警和监控）
app.include_router(alert_router.router)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware, logger_name="api")

# 安全中间件
@app.middleware("http")
async def security_middleware(request: Request, call_next):
    """安全中间件：速率限制、安全头等"""
    # 速率限制检查
    rate_limit_response = await rate_limiter.check_rate_limit(request)
    if rate_limit_response:
        return rate_limit_response

    # 执行请求
    response = await call_next(request)

    # 添加安全头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

    return response

@app.middleware("http")
async def ensure_utf8_json(request, call_next):
    response = await call_next(request)
    # 若为JSON响应，强制设置UTF-8编码头，避免客户端误判
    content_type = response.headers.get("content-type", "")
    if "application/json" in content_type:
        response.headers["Content-Type"] = "application/json; charset=utf-8"
    return response


@app.get("/health")
def health():
    return JSONResponse(content={"status": "ok", "message": "财经新闻Bot API运行正常"}, media_type="application/json; charset=utf-8")

@app.get("/db-check")
def db_check():
    url = os.getenv("DATABASE_URL")
    if not url:
        return {"ok": False, "error": "未设置 DATABASE_URL 环境变量"}
    try:
        scheme, rest = url.split("://", 1)
        creds, hostpart = rest.split("@", 1)
        user, pwd = creds.split(":", 1)
        hostport, *path = hostpart.split("/", 1)
        host, port = hostport.split(":")
        db = path[0].split("?")[0]
        conn = pymysql.connect(
            host=host,
            port=int(port),
            user=user,
            password=pwd,
            database=db,
            connect_timeout=5,
            ssl=None,
        )
        with conn.cursor() as c:
            c.execute("SELECT 1")
            row = c.fetchone()
        conn.close()
        return {"ok": True, "result": row}
    except Exception as e:
        return {"ok": False, "error": f"数据库连接检查失败：{str(e)}"}

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 财经新闻Bot API 启动中...")

    # 设置全局异常处理器
    setup_exception_handlers(app)
    logger.info("🛡️ 全局异常处理器已设置")

    logger.info(f"📊 日志级别: {logging.getLogger().level}")
    logger.info(f"📁 日志目录: logs/")
    logger.info("✅ 应用启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 财经新闻Bot API 关闭中...")
    logger.info("✅ 应用关闭完成")

