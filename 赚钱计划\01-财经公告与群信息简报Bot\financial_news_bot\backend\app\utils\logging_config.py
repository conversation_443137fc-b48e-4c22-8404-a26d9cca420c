"""
统一日志配置
提供标准化的日志格式和配置
"""
import os
import sys
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import json
import traceback

class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def __init__(self, service_name: str = "financial_news_bot"):
        super().__init__()
        self.service_name = service_name
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        # 基础日志信息
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "service": self.service_name,
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加线程和进程信息
        if hasattr(record, 'thread') and record.thread:
            log_entry["thread_id"] = record.thread
        if hasattr(record, 'process') and record.process:
            log_entry["process_id"] = record.process
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加自定义字段
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)

class StructuredFormatter(logging.Formatter):
    """结构化的文本日志格式化器"""
    
    def __init__(self, service_name: str = "financial_news_bot"):
        super().__init__()
        self.service_name = service_name
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为结构化文本"""
        timestamp = datetime.fromtimestamp(record.created).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        # 基础格式
        base_format = (
            f"[{timestamp}] "
            f"[{self.service_name}] "
            f"[{record.levelname:8}] "
            f"[{record.name}] "
            f"{record.getMessage()}"
        )
        
        # 添加位置信息
        location_info = f" ({record.module}.{record.funcName}:{record.lineno})"
        
        # 添加异常信息
        exception_info = ""
        if record.exc_info:
            exception_info = f"\n{self.formatException(record.exc_info)}"
        
        return base_format + location_info + exception_info

class LoggingConfig:
    """日志配置管理器"""
    
    def __init__(self, service_name: str = "financial_news_bot"):
        self.service_name = service_name
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志级别映射
        self.level_mapping = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
    
    def setup_logging(self, 
                     level: str = "INFO",
                     format_type: str = "structured",
                     enable_file_logging: bool = True,
                     enable_console_logging: bool = True,
                     max_file_size: int = 10 * 1024 * 1024,  # 10MB
                     backup_count: int = 5) -> None:
        """
        设置统一的日志配置
        
        Args:
            level: 日志级别
            format_type: 格式类型 ('json' 或 'structured')
            enable_file_logging: 是否启用文件日志
            enable_console_logging: 是否启用控制台日志
            max_file_size: 最大文件大小（字节）
            backup_count: 备份文件数量
        """
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 选择格式化器
        if format_type.lower() == "json":
            formatter = JSONFormatter(self.service_name)
        else:
            formatter = StructuredFormatter(self.service_name)
        
        # 设置控制台日志
        if enable_console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
            root_logger.addHandler(console_handler)
        
        # 设置文件日志
        if enable_file_logging:
            # 应用日志文件
            app_log_file = self.log_dir / f"{self.service_name}.log"
            app_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            app_handler.setFormatter(formatter)
            app_handler.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
            root_logger.addHandler(app_handler)
            
            # 错误日志文件
            error_log_file = self.log_dir / f"{self.service_name}_error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            root_logger.addHandler(error_handler)
        
        # 设置第三方库日志级别
        self._configure_third_party_loggers()
        
        logging.info(f"日志系统初始化完成 - 服务: {self.service_name}, 级别: {level}, 格式: {format_type}")
    
    def _configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        third_party_configs = {
            'httpx': logging.WARNING,
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'sqlalchemy': logging.WARNING,
            'alembic': logging.WARNING,
            'celery': logging.INFO,
            'redis': logging.WARNING,
            'asyncio': logging.WARNING
        }
        
        for logger_name, level in third_party_configs.items():
            logging.getLogger(logger_name).setLevel(level)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)
    
    def add_context_filter(self, **context):
        """添加上下文过滤器"""
        class ContextFilter(logging.Filter):
            def filter(self, record):
                for key, value in context.items():
                    setattr(record, key, value)
                return True
        
        root_logger = logging.getLogger()
        context_filter = ContextFilter()
        
        for handler in root_logger.handlers:
            handler.addFilter(context_filter)

class RequestLoggingMiddleware:
    """请求日志中间件"""

    def __init__(self, app, logger_name: str = "api"):
        self.app = app
        self.logger = logging.getLogger(logger_name)
    
    async def __call__(self, scope, receive, send):
        """处理请求日志 - ASGI中间件"""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        start_time = datetime.now()

        # 记录请求开始
        self.logger.info(
            "请求开始",
            extra={
                "method": scope.get("method"),
                "path": scope.get("path"),
                "client": scope.get("client", [None, None])[0] if scope.get("client") else None,
            }
        )

        try:
            # 处理请求
            await self.app(scope, receive, send)

            # 计算处理时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 记录请求完成
            self.logger.info(
                "请求完成",
                extra={
                    "method": scope.get("method"),
                    "path": scope.get("path"),
                    "duration_seconds": duration,
                }
            )

        except Exception as e:
            # 计算处理时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 记录请求异常
            self.logger.error(
                "请求异常",
                extra={
                    "method": scope.get("method"),
                    "path": scope.get("path"),
                    "duration_seconds": duration,
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                },
                exc_info=True
            )

            raise

def setup_application_logging(
    service_name: str = "financial_news_bot",
    level: str = None,
    format_type: str = None
) -> LoggingConfig:
    """
    设置应用程序日志
    
    Args:
        service_name: 服务名称
        level: 日志级别（从环境变量获取，默认INFO）
        format_type: 格式类型（从环境变量获取，默认structured）
    
    Returns:
        日志配置实例
    """
    # 从环境变量获取配置
    if level is None:
        level = os.getenv("LOG_LEVEL", "INFO")
    
    if format_type is None:
        format_type = os.getenv("LOG_FORMAT", "structured")
    
    # 创建并配置日志
    logging_config = LoggingConfig(service_name)
    logging_config.setup_logging(
        level=level,
        format_type=format_type,
        enable_file_logging=os.getenv("ENABLE_FILE_LOGGING", "true").lower() == "true",
        enable_console_logging=os.getenv("ENABLE_CONSOLE_LOGGING", "true").lower() == "true"
    )
    
    return logging_config

# 全局日志配置实例
_logging_config = None

def get_logging_config() -> LoggingConfig:
    """获取全局日志配置实例"""
    global _logging_config
    if _logging_config is None:
        _logging_config = setup_application_logging()
    return _logging_config

def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return get_logging_config().get_logger(name)
