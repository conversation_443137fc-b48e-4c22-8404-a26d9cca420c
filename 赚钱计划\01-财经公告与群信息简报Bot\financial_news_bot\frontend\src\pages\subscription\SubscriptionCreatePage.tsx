import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Steps,
  Button,
  Form,
  Input,
  Select,
  Checkbox,
  Radio,
  TimePicker,
  Tag,
  Space,
  Typography,
  Alert,
  message,
  Row,
  Col,
} from 'antd';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
} from '@ant-design/icons';

import { useAppDispatch } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';

const { Step } = Steps;
const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface SubscriptionData {
  name: string;
  description: string;
  topics: string[];
  keywords: string[];
  blacklist: string[];
  channels: {
    email: boolean;
    wechat: boolean;
    feishu: boolean;
    webhook: boolean;
  };
  schedule: {
    frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
    time?: string;
    days?: number[];
  };
  filters: {
    sources: string[];
    importance: string[];
    language: string;
  };
}

const SubscriptionCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();

  const [currentStep, setCurrentStep] = useState(0);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    name: '',
    description: '',
    topics: [],
    keywords: [],
    blacklist: [],
    channels: {
      email: true,
      wechat: false,
      feishu: false,
      webhook: false,
    },
    schedule: {
      frequency: 'daily',
      time: '09:00',
      days: [1, 2, 3, 4, 5],
    },
    filters: {
      sources: [],
      importance: ['high', 'medium'],
      language: 'zh',
    },
  });

  React.useEffect(() => {
    dispatch(setPageTitle('创建订阅'));
  }, [dispatch]);

  const steps = [
    {
      title: '基本信息',
      description: '设置订阅名称和描述',
    },
    {
      title: '主题关键词',
      description: '选择关注的主题和关键词',
    },
    {
      title: '推送渠道',
      description: '配置推送方式和时间',
    },
    {
      title: '过滤规则',
      description: '设置内容过滤条件',
    },
    {
      title: '确认创建',
      description: '预览并确认订阅配置',
    },
  ];

  // 下一步
  const handleNext = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();
      setSubscriptionData(prev => ({ ...prev, ...values }));
      setCurrentStep(prev => prev + 1);
    } catch (error) {
      // 表单验证失败日志 - 用于调试表单验证问题
      console.error('表单验证失败:', error);
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(prev => prev - 1);
  };

  // 完成创建
  const handleFinish = async () => {
    try {
      // 这里调用API创建订阅
      message.success('订阅创建成功！');
      navigate('/subscriptions');
    } catch {
      message.error('创建订阅失败');
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Form
            form={form}
            layout="vertical"
            initialValues={subscriptionData}
            size="large"
          >
            <Form.Item
              name="name"
              label="订阅名称"
              rules={[
                { required: true, message: '请输入订阅名称' },
                { max: 50, message: '订阅名称不能超过50个字符' },
              ]}
            >
              <Input placeholder="例如：科技新闻日报" />
            </Form.Item>

            <Form.Item
              name="description"
              label="订阅描述"
              rules={[
                { max: 200, message: '描述不能超过200个字符' },
              ]}
            >
              <TextArea
                rows={4}
                placeholder="简要描述这个订阅的用途和内容..."
              />
            </Form.Item>

            <Alert
              message="提示"
              description="订阅名称将显示在推送消息的标题中，建议使用简洁明了的名称。"
              type="info"
              showIcon
              style={{ marginTop: '1rem' }}
            />
          </Form>
        );

      case 1:
        return (
          <Form
            form={form}
            layout="vertical"
            initialValues={subscriptionData}
            size="large"
          >
            <Form.Item
              name="topics"
              label="关注主题"
              rules={[{ required: true, message: '请至少选择一个主题' }]}
            >
              <Checkbox.Group>
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Checkbox value="finance">财经新闻</Checkbox>
                  </Col>
                  <Col span={8}>
                    <Checkbox value="technology">科技资讯</Checkbox>
                  </Col>
                  <Col span={8}>
                    <Checkbox value="market">市场动态</Checkbox>
                  </Col>
                  <Col span={8}>
                    <Checkbox value="policy">政策法规</Checkbox>
                  </Col>
                  <Col span={8}>
                    <Checkbox value="industry">行业分析</Checkbox>
                  </Col>
                  <Col span={8}>
                    <Checkbox value="international">国际新闻</Checkbox>
                  </Col>
                </Row>
              </Checkbox.Group>
            </Form.Item>

            <Form.Item
              name="keywords"
              label="关键词"
              extra="输入关键词后按回车添加，支持中英文"
            >
              <Select
                mode="tags"
                placeholder="输入关键词，如：人工智能、区块链、新能源..."
                style={{ width: '100%' }}
                tokenSeparators={[',', ' ']}
              />
            </Form.Item>

            <Form.Item
              name="blacklist"
              label="排除关键词"
              extra="包含这些关键词的新闻将被过滤掉"
            >
              <Select
                mode="tags"
                placeholder="输入要排除的关键词..."
                style={{ width: '100%' }}
                tokenSeparators={[',', ' ']}
              />
            </Form.Item>
          </Form>
        );

      case 2:
        return (
          <Form
            form={form}
            layout="vertical"
            initialValues={subscriptionData}
            size="large"
          >
            <Form.Item label="推送渠道">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Form.Item name={['channels', 'email']} valuePropName="checked" noStyle>
                  <Checkbox>邮箱推送</Checkbox>
                </Form.Item>
                <Form.Item name={['channels', 'wechat']} valuePropName="checked" noStyle>
                  <Checkbox>企业微信</Checkbox>
                </Form.Item>
                <Form.Item name={['channels', 'feishu']} valuePropName="checked" noStyle>
                  <Checkbox>飞书</Checkbox>
                </Form.Item>
                <Form.Item name={['channels', 'webhook']} valuePropName="checked" noStyle>
                  <Checkbox>Webhook</Checkbox>
                </Form.Item>
              </Space>
            </Form.Item>

            <Form.Item
              name={['schedule', 'frequency']}
              label="推送频率"
              rules={[{ required: true, message: '请选择推送频率' }]}
            >
              <Radio.Group>
                <Space direction="vertical">
                  <Radio value="realtime">实时推送</Radio>
                  <Radio value="hourly">每小时汇总</Radio>
                  <Radio value="daily">每日汇总</Radio>
                  <Radio value="weekly">每周汇总</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name={['schedule', 'time']}
              label="推送时间"
              extra="设置每日推送的具体时间"
            >
              <TimePicker
                format="HH:mm"
                placeholder="选择推送时间"
                style={{ width: '200px' }}
              />
            </Form.Item>

            <Form.Item
              name={['schedule', 'days']}
              label="推送日期"
              extra="选择一周中的哪些天进行推送"
            >
              <Checkbox.Group>
                <Row>
                  <Col span={3}><Checkbox value={1}>周一</Checkbox></Col>
                  <Col span={3}><Checkbox value={2}>周二</Checkbox></Col>
                  <Col span={3}><Checkbox value={3}>周三</Checkbox></Col>
                  <Col span={3}><Checkbox value={4}>周四</Checkbox></Col>
                  <Col span={3}><Checkbox value={5}>周五</Checkbox></Col>
                  <Col span={3}><Checkbox value={6}>周六</Checkbox></Col>
                  <Col span={3}><Checkbox value={0}>周日</Checkbox></Col>
                </Row>
              </Checkbox.Group>
            </Form.Item>
          </Form>
        );

      case 3:
        return (
          <Form
            form={form}
            layout="vertical"
            initialValues={subscriptionData}
            size="large"
          >
            <Form.Item
              name={['filters', 'sources']}
              label="新闻来源"
              extra="选择信任的新闻源，留空表示不限制"
            >
              <Select
                mode="multiple"
                placeholder="选择新闻来源..."
                style={{ width: '100%' }}
              >
                <Option value="xinhua">新华网</Option>
                <Option value="people">人民网</Option>
                <Option value="caixin">财新网</Option>
                <Option value="36kr">36氪</Option>
                <Option value="techcrunch">TechCrunch</Option>
                <Option value="reuters">路透社</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['filters', 'importance']}
              label="重要程度"
              rules={[{ required: true, message: '请选择重要程度' }]}
            >
              <Checkbox.Group>
                <Space>
                  <Checkbox value="high">高</Checkbox>
                  <Checkbox value="medium">中</Checkbox>
                  <Checkbox value="low">低</Checkbox>
                </Space>
              </Checkbox.Group>
            </Form.Item>

            <Form.Item
              name={['filters', 'language']}
              label="语言"
              rules={[{ required: true, message: '请选择语言' }]}
            >
              <Radio.Group>
                <Radio value="zh">中文</Radio>
                <Radio value="en">英文</Radio>
                <Radio value="all">不限</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        );

      case 4:
        return (
          <div>
            <Title level={4}>订阅配置预览</Title>
            <Card>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>订阅名称：</Text>
                  <Text>{subscriptionData.name}</Text>
                </div>
                <div>
                  <Text strong>描述：</Text>
                  <Text>{subscriptionData.description}</Text>
                </div>
                <div>
                  <Text strong>关注主题：</Text>
                  <Space wrap>
                    {subscriptionData.topics.map(topic => (
                      <Tag key={topic} color="blue">{topic}</Tag>
                    ))}
                  </Space>
                </div>
                <div>
                  <Text strong>关键词：</Text>
                  <Space wrap>
                    {subscriptionData.keywords.map(keyword => (
                      <Tag key={keyword}>{keyword}</Tag>
                    ))}
                  </Space>
                </div>
                <div>
                  <Text strong>推送频率：</Text>
                  <Text>{subscriptionData.schedule.frequency}</Text>
                </div>
              </Space>
            </Card>

            <Alert
              message="确认创建"
              description="请确认以上配置信息无误，创建后可以在订阅管理中修改。"
              type="info"
              showIcon
              style={{ marginTop: '1rem' }}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <div style={{ marginBottom: '2rem' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/subscriptions')}
          style={{ marginBottom: '1rem' }}
        >
          返回订阅列表
        </Button>
        <Title level={2}>创建新订阅</Title>
      </div>

      <Card>
        <Steps current={currentStep} style={{ marginBottom: '2rem' }}>
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
            />
          ))}
        </Steps>

        <div style={{ minHeight: '400px', marginBottom: '2rem' }}>
          {renderStepContent()}
        </div>

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button type="primary" onClick={handleNext}>
                下一步
                <ArrowRightOutlined />
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button type="primary" onClick={handleFinish}>
                <CheckOutlined />
                创建订阅
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SubscriptionCreatePage;
