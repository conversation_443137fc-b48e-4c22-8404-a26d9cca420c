import { useState, useCallback, useEffect } from 'react';

// 验证规则类型
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  number?: boolean;
  min?: number;
  max?: number;
  custom?: (_value: unknown) => string | null;
  message?: string;
}

// 字段验证配置
export interface FieldConfig {
  rules: ValidationRule[];
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  error: string | null;
  touched: boolean;
}

// 表单状态
export interface FormState<T> {
  values: T;
  errors: Record<keyof T, string | null>;
  touched: Record<keyof T, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
}

// 表单配置
export interface FormConfig<T> {
  initialValues: T;
  validationSchema: Record<keyof T, FieldConfig>;
  onSubmit?: (_values: T) => Promise<void> | void;
  validateOnMount?: boolean;
}

// 预定义验证规则
export const validationRules = {
  required: (message = '此字段为必填项'): ValidationRule => ({
    required: true,
    message,
  }),

  email: (message = '请输入有效的邮箱地址'): ValidationRule => ({
    email: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message,
  }),

  phone: (message = '请输入有效的手机号码'): ValidationRule => ({
    phone: true,
    pattern: /^1[3-9]\d{9}$/,
    message,
  }),

  password: (message = '密码至少8位，包含字母和数字'): ValidationRule => ({
    minLength: 8,
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
    message,
  }),

  username: (message = '用户名3-20位，只能包含字母、数字和下划线'): ValidationRule => ({
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_]+$/,
    message,
  }),

  url: (message = '请输入有效的URL地址'): ValidationRule => ({
    url: true,
    pattern: /^https?:\/\/.+/,
    message,
  }),

  minLength: (min: number, message?: string): ValidationRule => ({
    minLength: min,
    message: message || `最少输入${min}个字符`,
  }),

  maxLength: (max: number, message?: string): ValidationRule => ({
    maxLength: max,
    message: message || `最多输入${max}个字符`,
  }),

  range: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    message: message || `请输入${min}-${max}之间的数值`,
  }),

  custom: (validator: (_value: unknown) => string | null): ValidationRule => ({
    custom: validator,
  }),
};

// 验证单个字段
const validateField = (value: unknown, rules: ValidationRule[]): string | null => {
  for (const rule of rules) {
    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return rule.message || '此字段为必填项';
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!value && !rule.required) {
      continue;
    }

    const stringValue = String(value);

    // 长度验证
    if (rule.minLength && stringValue.length < rule.minLength) {
      return rule.message || `最少输入${rule.minLength}个字符`;
    }

    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return rule.message || `最多输入${rule.maxLength}个字符`;
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || '格式不正确';
    }

    // 邮箱验证
    if (rule.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(stringValue)) {
      return rule.message || '请输入有效的邮箱地址';
    }

    // 手机号验证
    if (rule.phone && !/^1[3-9]\d{9}$/.test(stringValue)) {
      return rule.message || '请输入有效的手机号码';
    }

    // URL验证
    if (rule.url && !/^https?:\/\/.+/.test(stringValue)) {
      return rule.message || '请输入有效的URL地址';
    }

    // 数值验证
    if (rule.number) {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return rule.message || '请输入有效的数字';
      }

      if (rule.min !== undefined && numValue < rule.min) {
        return rule.message || `数值不能小于${rule.min}`;
      }

      if (rule.max !== undefined && numValue > rule.max) {
        return rule.message || `数值不能大于${rule.max}`;
      }
    }

    // 自定义验证
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        return customError;
      }
    }
  }

  return null;
};

// 表单验证Hook
export const useFormValidation = <T extends Record<string, unknown>>(
  config: FormConfig<T>
) => {
  const [formState, setFormState] = useState<FormState<T>>(() => ({
    values: config.initialValues,
    errors: {} as Record<keyof T, string | null>,
    touched: {} as Record<keyof T, boolean>,
    isValid: false,
    isSubmitting: false,
  }));

  const [debounceTimers, setDebounceTimers] = useState<Record<string, NodeJS.Timeout>>({});

  // 验证单个字段
  const validateSingleField = useCallback((
    fieldName: keyof T,
    value: unknown,
    shouldTouch = true
  ) => {
    const fieldConfig = config.validationSchema[fieldName];
    if (!fieldConfig) return;

    const error = validateField(value, fieldConfig.rules);

    setFormState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [fieldName]: error,
      },
      touched: shouldTouch ? {
        ...prev.touched,
        [fieldName]: true,
      } : prev.touched,
    }));

    return error;
  }, [config.validationSchema]);

  // 验证整个表单
  const validateForm = useCallback(() => {
    const errors: Record<keyof T, string | null> = {} as Record<keyof T, string | null>;
    let isValid = true;

    Object.keys(config.validationSchema).forEach(fieldName => {
      const key = fieldName as keyof T;
      const fieldConfig = config.validationSchema[key];
      const value = formState.values[key];
      
      const error = validateField(value, fieldConfig.rules);
      errors[key] = error;
      
      if (error) {
        isValid = false;
      }
    });

    setFormState(prev => ({
      ...prev,
      errors,
      isValid,
      touched: Object.keys(config.validationSchema).reduce((acc, key) => ({
        ...acc,
        [key]: true,
      }), {} as Record<keyof T, boolean>),
    }));

    return isValid;
  }, [config.validationSchema, formState.values]);

  // 设置字段值
  const setFieldValue = useCallback((fieldName: keyof T, value: unknown) => {
    setFormState(prev => ({
      ...prev,
      values: {
        ...prev.values,
        [fieldName]: value,
      },
    }));

    const fieldConfig = config.validationSchema[fieldName];
    if (fieldConfig?.validateOnChange) {
      if (fieldConfig.debounceMs) {
        // 清除之前的定时器
        if (debounceTimers[fieldName as string]) {
          clearTimeout(debounceTimers[fieldName as string]);
        }

        // 设置新的定时器
        const timer = setTimeout(() => {
          validateSingleField(fieldName, value);
        }, fieldConfig.debounceMs);

        setDebounceTimers(prev => ({
          ...prev,
          [fieldName]: timer,
        }));
      } else {
        validateSingleField(fieldName, value);
      }
    }
  }, [config.validationSchema, debounceTimers, validateSingleField]);

  // 设置字段触摸状态
  const setFieldTouched = useCallback((fieldName: keyof T, touched = true) => {
    setFormState(prev => ({
      ...prev,
      touched: {
        ...prev.touched,
        [fieldName]: touched,
      },
    }));

    const fieldConfig = config.validationSchema[fieldName];
    if (touched && fieldConfig?.validateOnBlur) {
      validateSingleField(fieldName, formState.values[fieldName], false);
    }
  }, [config.validationSchema, formState.values, validateSingleField]);

  // 重置表单
  const resetForm = useCallback(() => {
    setFormState({
      values: config.initialValues,
      errors: {} as Record<keyof T, string | null>,
      touched: {} as Record<keyof T, boolean>,
      isValid: false,
      isSubmitting: false,
    });

    // 清除所有定时器
    Object.values(debounceTimers).forEach(timer => clearTimeout(timer));
    setDebounceTimers({});
  }, [config.initialValues, debounceTimers]);

  // 提交表单
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    const isValid = validateForm();
    if (!isValid || !config.onSubmit) {
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      await config.onSubmit(formState.values);
    } catch (error) {
      // 表单提交失败日志 - 用于监控表单提交问题
      console.error('表单提交失败:', error);
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [validateForm, config.onSubmit, formState.values]);

  // 获取字段属性
  const getFieldProps = useCallback((fieldName: keyof T) => ({
    value: formState.values[fieldName],
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFieldValue(fieldName, e.target.value);
    },
    onBlur: () => {
      setFieldTouched(fieldName, true);
    },
    status: formState.touched[fieldName] && formState.errors[fieldName] ? 'error' : undefined,
  }), [formState, setFieldValue, setFieldTouched]);

  // 获取字段错误信息
  const getFieldError = useCallback((fieldName: keyof T) => {
    return formState.touched[fieldName] ? formState.errors[fieldName] : null;
  }, [formState.touched, formState.errors]);

  // 初始验证
  useEffect(() => {
    if (config.validateOnMount) {
      validateForm();
    }
  }, [config.validateOnMount, validateForm]);

  // 清理定时器
  useEffect(() => {
    return () => {
      Object.values(debounceTimers).forEach(timer => clearTimeout(timer));
    };
  }, [debounceTimers]);

  return {
    values: formState.values,
    errors: formState.errors,
    touched: formState.touched,
    isValid: formState.isValid,
    isSubmitting: formState.isSubmitting,
    setFieldValue,
    setFieldTouched,
    validateField: validateSingleField,
    validateForm,
    resetForm,
    handleSubmit,
    getFieldProps,
    getFieldError,
  };
};
