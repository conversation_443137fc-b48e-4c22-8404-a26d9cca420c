import React, { useState } from 'react';
import {
  Card,
  Input,
  Button,
  Tag,
  Space,
  Select,
  Slider,
  Switch,
  Typography,
  Row,
  Col,
  message,
  Tooltip,
  Modal,
  Form,
  AutoComplete,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  <PERSON>boltOutlined,
  FilterOutlined,
} from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;

interface Keyword {
  id: string;
  text: string;
  type: 'include' | 'exclude';
  weight: number;
  isRegex: boolean;
  category: string;
}

interface KeywordManagerProps {
  keywords: Keyword[];
  onChange: (_keywords: Keyword[]) => void;
}

const KeywordManager: React.FC<KeywordManagerProps> = ({ keywords, onChange }) => {
  const [form] = Form.useForm();
  const [newKeyword, setNewKeyword] = useState('');
  const [editingKeyword, setEditingKeyword] = useState<Keyword | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // 推荐关键词
  const recommendedKeywords = [
    '人工智能', '区块链', '新能源', '芯片', '5G',
    '股市', '基金', '债券', '外汇', '期货',
    '央行', '货币政策', '利率', 'GDP', 'CPI',
    '科技股', '新股上市', '并购重组', '业绩预告',
  ];

  const categories = [
    { value: 'technology', label: '科技' },
    { value: 'finance', label: '金融' },
    { value: 'policy', label: '政策' },
    { value: 'market', label: '市场' },
    { value: 'industry', label: '行业' },
  ];

  const handleAddKeyword = () => {
    if (!newKeyword.trim()) {
      message.warning('请输入关键词');
      return;
    }

    const keyword: Keyword = {
      id: Date.now().toString(),
      text: newKeyword.trim(),
      type: 'include',
      weight: 1,
      isRegex: false,
      category: 'general',
    };

    onChange([...keywords, keyword]);
    setNewKeyword('');
    message.success('关键词添加成功');
  };

  const handleDeleteKeyword = (id: string) => {
    onChange(keywords.filter(k => k.id !== id));
    message.success('关键词删除成功');
  };

  const handleEditKeyword = (keyword: Keyword) => {
    setEditingKeyword(keyword);
    form.setFieldsValue(keyword);
    setModalVisible(true);
  };

  const handleUpdateKeyword = (values: Record<string, unknown>) => {
    if (!editingKeyword) return;

    const updatedKeywords = keywords.map(k =>
      k.id === editingKeyword.id ? { ...k, ...values } : k
    );
    onChange(updatedKeywords);
    setModalVisible(false);
    setEditingKeyword(null);
    message.success('关键词更新成功');
  };

  const handleQuickAdd = (keyword: string) => {
    const newKeywordObj: Keyword = {
      id: Date.now().toString(),
      text: keyword,
      type: 'include',
      weight: 1,
      isRegex: false,
      category: 'general',
    };
    onChange([...keywords, newKeywordObj]);
    message.success(`已添加关键词：${keyword}`);
  };

  const getKeywordColor = (keyword: Keyword) => {
    if (keyword.type === 'exclude') return 'red';
    if (keyword.weight > 2) return 'gold';
    if (keyword.isRegex) return 'purple';
    return 'blue';
  };

  const handleSearch = (value: string) => {
    const filtered = recommendedKeywords.filter(keyword =>
      keyword.toLowerCase().includes(value.toLowerCase())
    );
    setSuggestions(filtered);
  };

  return (
    <div>
      <Title level={4}>关键词管理</Title>
      
      {/* 添加关键词 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={8} align="middle">
          <Col flex="auto">
            <AutoComplete
              value={newKeyword}
              onChange={setNewKeyword}
              onSearch={handleSearch}
              options={suggestions.map(item => ({ value: item }))}
              placeholder="输入关键词，支持正则表达式"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddKeyword();
                }
              }}
            />
          </Col>
          <Col>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddKeyword}>
              添加
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 推荐关键词 */}
      <Card size="small" title="推荐关键词" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {recommendedKeywords.map(keyword => (
            <Tag
              key={keyword}
              style={{ cursor: 'pointer' }}
              onClick={() => handleQuickAdd(keyword)}
            >
              <PlusOutlined style={{ marginRight: '4px' }} />
              {keyword}
            </Tag>
          ))}
        </Space>
      </Card>

      {/* 关键词列表 */}
      <Card title="已添加的关键词" size="small">
        {keywords.length === 0 ? (
          <Text type="secondary">暂无关键词，请添加关键词</Text>
        ) : (
          <Space wrap>
            {keywords.map(keyword => (
              <Tag
                key={keyword.id}
                color={getKeywordColor(keyword)}
                style={{ marginBottom: '8px', padding: '4px 8px' }}
              >
                <Space size="small">
                  <span>{keyword.text}</span>
                  {keyword.isRegex && (
                    <Tooltip title="正则表达式">
                      <ThunderboltOutlined style={{ fontSize: '12px' }} />
                    </Tooltip>
                  )}
                  {keyword.weight > 1 && (
                    <Tooltip title={`权重: ${keyword.weight}`}>
                      <span style={{ fontSize: '10px' }}>×{keyword.weight}</span>
                    </Tooltip>
                  )}
                  {keyword.type === 'exclude' && (
                    <Tooltip title="排除关键词">
                      <FilterOutlined style={{ fontSize: '12px' }} />
                    </Tooltip>
                  )}
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditKeyword(keyword)}
                    style={{ padding: 0, height: 'auto' }}
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteKeyword(keyword.id)}
                    style={{ padding: 0, height: 'auto', color: '#ff4d4f' }}
                  />
                </Space>
              </Tag>
            ))}
          </Space>
        )}
      </Card>

      {/* 编辑关键词模态框 */}
      <Modal
        title="编辑关键词"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateKeyword}
        >
          <Form.Item
            name="text"
            label="关键词"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <Input placeholder="请输入关键词" />
          </Form.Item>

          <Form.Item name="type" label="类型">
            <Select>
              <Option value="include">包含</Option>
              <Option value="exclude">排除</Option>
            </Select>
          </Form.Item>

          <Form.Item name="category" label="分类">
            <Select>
              {categories.map(cat => (
                <Option key={cat.value} value={cat.value}>
                  {cat.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="weight"
            label={
              <Space>
                权重
                <Tooltip title="权重越高，包含该关键词的新闻优先级越高">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
          >
            <Slider min={1} max={5} marks={{ 1: '1', 3: '3', 5: '5' }} />
          </Form.Item>

          <Form.Item
            name="isRegex"
            label={
              <Space>
                正则表达式
                <Tooltip title="启用后将按正则表达式匹配">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KeywordManager;
